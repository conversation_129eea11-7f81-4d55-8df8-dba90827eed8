import { prisma } from '@/lib/prisma';
import { generateAllDailyReadings, generateDailyZodiacReading } from '@/lib/gemini';
import { translateAllDailyReadings } from '@/lib/gemini-translation';
import { ZodiacSign } from '@/types';

class DailyReadingsScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastGeneratedDate: string | null = null;

  constructor() {
    this.start();
  }

  start() {
    if (this.isRunning) {
      console.log('📅 Daily readings scheduler is already running');
      return;
    }

    console.log('🚀 Starting daily readings scheduler...');
    this.isRunning = true;

    // Check immediately on startup for current Sri Lankan date
    console.log('🔍 Checking for missing readings on startup...');
    this.checkAndGenerateReadings().catch(error => {
      console.error('❌ Error during startup reading check:', error);
    });

    // Schedule to run daily at 00:01 AM Sri Lankan time
    this.scheduleDaily();

    console.log('✅ Daily readings scheduler started - will run daily at 00:01 AM Sri Lankan time');
  }

  private scheduleDaily() {
    // Calculate time until next 00:01 AM Sri Lankan time
    const now = new Date();
    const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
    const sriLankanTime = new Date(sriLankanTimeString);

    // Set target time to 00:01 AM Sri Lankan time
    const nextRun = new Date(sriLankanTime);
    nextRun.setHours(0, 1, 0, 0); // 00:01:00.000

    // If it's already past 00:01 AM today, schedule for tomorrow
    if (sriLankanTime >= nextRun) {
      nextRun.setDate(nextRun.getDate() + 1);
    }

    // Convert back to local time for setTimeout
    const timeUntilNextRun = nextRun.getTime() - sriLankanTime.getTime();

    console.log(`⏰ Next daily reading generation scheduled for: ${nextRun.toLocaleString("en-US", {timeZone: "Asia/Colombo"})} Sri Lankan time`);
    console.log(`⏱️ Time until next run: ${Math.round(timeUntilNextRun / (1000 * 60 * 60))} hours and ${Math.round((timeUntilNextRun % (1000 * 60 * 60)) / (1000 * 60))} minutes`);

    // Set timeout for the next run
    this.intervalId = setTimeout(() => {
      this.checkAndGenerateReadings();
      // After running, schedule the next daily run (24 hours later)
      this.scheduleDaily();
    }, timeUntilNextRun);
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('🛑 Daily readings scheduler stopped');
  }

  async checkAndGenerateReadings() {
    try {
      // Get current Sri Lankan date properly
      const now = new Date();

      // Create a proper Sri Lankan time date object
      const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
      const sriLankanTime = new Date(sriLankanTimeString);

      // Get the date in YYYY-MM-DD format for Sri Lankan timezone
      const today = sriLankanTime.getFullYear() + '-' +
                   String(sriLankanTime.getMonth() + 1).padStart(2, '0') + '-' +
                   String(sriLankanTime.getDate()).padStart(2, '0');

      console.log(`🕐 Checking daily readings for ${today} (Sri Lankan date) at ${sriLankanTime.toLocaleTimeString()}`);
      console.log(`🌍 Server time: ${now.toLocaleTimeString()}, Sri Lankan time: ${sriLankanTime.toLocaleTimeString()}`);
      console.log(`📅 Server date: ${now.toISOString().split('T')[0]}, Sri Lankan date: ${today}`);

      // Check if readings already exist for today (both languages)
      const existingEnglishReadings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(today),
          language: 'en'
        }
      });

      const existingSinhalaReadings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(today),
          language: 'si'
        }
      });

      if (existingEnglishReadings.length === 12 && existingSinhalaReadings.length === 12) {
        console.log(`✅ Daily readings already exist for ${today} in both languages (${existingEnglishReadings.length} English + ${existingSinhalaReadings.length} Sinhala)`);
        this.lastGeneratedDate = today;
        return;
      }

      console.log(`🤖 Generating daily zodiac readings for ${today}...`);
      console.log(`📊 Found ${existingEnglishReadings.length}/12 existing English readings`);
      console.log(`📊 Found ${existingSinhalaReadings.length}/12 existing Sinhala readings`);

      // Handle English readings first
      let allEnglishReadings = existingEnglishReadings;
      if (existingEnglishReadings.length < 12) {
        // Determine which zodiac signs are missing for English
        const existingEnglishSigns = existingEnglishReadings.map(r => r.zodiacSign);
        const allSigns: any[] = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
        const missingEnglishSigns = allSigns.filter(sign => !existingEnglishSigns.includes(sign));

        console.log(`🎯 Missing English readings for: ${missingEnglishSigns.join(', ')}`);

        // Generate English readings only for missing signs to avoid unnecessary API calls
        let newEnglishReadings = [];
        if (missingEnglishSigns.length > 0) {
          console.log(`🤖 Generating English readings for ${missingEnglishSigns.length} missing signs...`);

          // Generate readings for missing signs only
          for (const sign of missingEnglishSigns) {
            try {
              console.log(`🔄 Generating English reading for ${sign}...`);
              const reading = await this.generateSingleReading(sign, today);
              if (reading) {
                newEnglishReadings.push(reading);
                console.log(`✅ Generated English reading for ${sign}`);
              }
              // Small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
              console.error(`❌ Error generating English reading for ${sign}:`, error);
            }
          }
        }

        console.log(`🎯 Generated ${newEnglishReadings.length} new English readings from Gemini API`);

        // Save English readings to database
        const savedEnglishReadings = [];
        for (const reading of newEnglishReadings) {
          try {
            const saved = await prisma.dailyZodiacReading.upsert({
              where: {
                zodiacSign_date_language: {
                  zodiacSign: reading.zodiacSign,
                  date: new Date(today),
                  language: 'en'
                }
              },
              update: {
                generalReading: reading.generalReading,
                loveReading: reading.loveReading,
                careerReading: reading.careerReading,
                healthReading: reading.healthReading,
                luckyNumber: reading.luckyNumber,
                luckyColor: reading.luckyColor,
                luckyTime: reading.luckyTime,
                luckyGem: reading.luckyGem,
                advice: reading.advice,
                mood: reading.mood,
                compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                updatedAt: new Date()
              },
              create: {
                zodiacSign: reading.zodiacSign,
                date: new Date(today),
                generalReading: reading.generalReading,
                loveReading: reading.loveReading,
                careerReading: reading.careerReading,
                healthReading: reading.healthReading,
                luckyNumber: reading.luckyNumber,
                luckyColor: reading.luckyColor,
                luckyTime: reading.luckyTime,
                luckyGem: reading.luckyGem,
                advice: reading.advice,
                mood: reading.mood,
                compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                language: 'en'
              }
            });
            savedEnglishReadings.push(saved);
            console.log(`✅ Saved English reading for ${reading.zodiacSign}`);
          } catch (error) {
            console.error(`❌ Error saving English reading for ${reading.zodiacSign}:`, error);
          }
        }

        allEnglishReadings = [...existingEnglishReadings, ...savedEnglishReadings];
        console.log(`🎉 Successfully saved ${savedEnglishReadings.length} new English readings`);
      }

      // Handle Sinhala translations
      let allSinhalaReadings = existingSinhalaReadings;
      if (existingSinhalaReadings.length < 12 && allEnglishReadings.length === 12) {
        console.log(`🔄 Generating Sinhala translations for ${today}...`);

        try {
          const translatedReadings = await translateAllDailyReadings(allEnglishReadings, 'si');

          // Save Sinhala readings to database
          const savedSinhalaReadings = [];
          for (const reading of translatedReadings) {
            try {
              const saved = await prisma.dailyZodiacReading.upsert({
                where: {
                  zodiacSign_date_language: {
                    zodiacSign: reading.zodiacSign,
                    date: new Date(today),
                    language: 'si'
                  }
                },
                update: {
                  generalReading: reading.generalReading,
                  loveReading: reading.loveReading,
                  careerReading: reading.careerReading,
                  healthReading: reading.healthReading,
                  luckyNumber: reading.luckyNumber,
                  luckyColor: reading.luckyColor,
                  luckyTime: reading.luckyTime,
                  luckyGem: reading.luckyGem,
                  advice: reading.advice,
                  mood: reading.mood,
                  compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                  updatedAt: new Date()
                },
                create: {
                  zodiacSign: reading.zodiacSign,
                  date: new Date(today),
                  generalReading: reading.generalReading,
                  loveReading: reading.loveReading,
                  careerReading: reading.careerReading,
                  healthReading: reading.healthReading,
                  luckyNumber: reading.luckyNumber,
                  luckyColor: reading.luckyColor,
                  luckyTime: reading.luckyTime,
                  luckyGem: reading.luckyGem,
                  advice: reading.advice,
                  mood: reading.mood,
                  compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                  language: 'si'
                }
              });
              savedSinhalaReadings.push(saved);
              console.log(`✅ Saved Sinhala reading for ${reading.zodiacSign}`);
            } catch (error) {
              console.error(`❌ Error saving Sinhala reading for ${reading.zodiacSign}:`, error);
            }
          }

          allSinhalaReadings = [...existingSinhalaReadings, ...savedSinhalaReadings];
          console.log(`🎉 Successfully saved ${savedSinhalaReadings.length} Sinhala translations`);

        } catch (error) {
          console.error('❌ Error generating Sinhala translations:', error);
        }
      }

      this.lastGeneratedDate = today;
      const totalReadings = allEnglishReadings.length + allSinhalaReadings.length;
      console.log(`🎉 Successfully processed daily readings for ${today}: ${allEnglishReadings.length} English + ${allSinhalaReadings.length} Sinhala = ${totalReadings} total`);

      // Clean up old readings (keep only last 30 days)
      await this.cleanupOldReadings();

    } catch (error) {
      console.error('❌ Error in daily readings scheduler:', error);
    }
  }

  private async generateSingleReading(zodiacSign: ZodiacSign, date: string) {
    try {
      const reading = await generateDailyZodiacReading(zodiacSign, date);
      return reading;
    } catch (error) {
      console.error(`❌ Error generating single reading for ${zodiacSign}:`, error);
      return null;
    }
  }

  private async cleanupOldReadings() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedCount = await prisma.dailyZodiacReading.deleteMany({
        where: {
          date: {
            lt: thirtyDaysAgo
          }
        }
      });

      if (deletedCount.count > 0) {
        console.log(`🧹 Cleaned up ${deletedCount.count} old daily readings`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up old readings:', error);
    }
  }

  // Method to manually trigger reading generation (for testing)
  async generateNow(date?: string) {
    // Use Sri Lankan date if no specific date provided
    let targetDate = date;
    if (!targetDate) {
      const now = new Date();
      const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
      const sriLankanTime = new Date(sriLankanTimeString);
      targetDate = sriLankanTime.getFullYear() + '-' +
                   String(sriLankanTime.getMonth() + 1).padStart(2, '0') + '-' +
                   String(sriLankanTime.getDate()).padStart(2, '0');
    }

    console.log(`🔄 Manually generating readings for ${targetDate}...`);
    
    try {
      const readings = await generateAllDailyReadings(targetDate);
      
      const savedReadings = [];
      for (const reading of readings) {
        const saved = await prisma.dailyZodiacReading.upsert({
          where: {
            zodiacSign_date_language: {
              zodiacSign: reading.zodiacSign,
              date: new Date(targetDate),
              language: 'en'
            }
          },
          update: {
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
            updatedAt: new Date()
          },
          create: {
            zodiacSign: reading.zodiacSign,
            date: new Date(targetDate),
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
            language: 'en'
          }
        });
        savedReadings.push(saved);
      }

      console.log(`✅ Manually generated ${savedReadings.length} readings for ${targetDate}`);
      return savedReadings;
    } catch (error) {
      console.error('❌ Error in manual generation:', error);
      throw error;
    }
  }

  // Method to force immediate check and generation for current Sri Lankan date
  async forceCheckNow() {
    console.log('🔄 Force checking and generating readings for current Sri Lankan date...');
    await this.checkAndGenerateReadings();
  }

  getStatus() {
    let nextCheck = 'Not scheduled';

    if (this.intervalId && this.isRunning) {
      // Calculate next 00:01 AM Sri Lankan time
      const now = new Date();
      const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
      const sriLankanTime = new Date(sriLankanTimeString);
      const nextRun = new Date(sriLankanTime);
      nextRun.setHours(0, 1, 0, 0);

      if (sriLankanTime >= nextRun) {
        nextRun.setDate(nextRun.getDate() + 1);
      }

      nextCheck = `Daily at 00:01 AM Sri Lankan time (Next: ${nextRun.toLocaleString("en-US", {timeZone: "Asia/Colombo"})})`;
    }

    return {
      isRunning: this.isRunning,
      lastGeneratedDate: this.lastGeneratedDate,
      nextCheck: nextCheck,
      currentSriLankanTime: new Date().toLocaleString("en-US", {timeZone: "Asia/Colombo"})
    };
  }
}

// Create singleton instance
export const dailyReadingsScheduler = new DailyReadingsScheduler();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 Received SIGTERM, stopping scheduler...');
  dailyReadingsScheduler.stop();
});

process.on('SIGINT', () => {
  console.log('🔄 Received SIGINT, stopping scheduler...');
  dailyReadingsScheduler.stop();
});
