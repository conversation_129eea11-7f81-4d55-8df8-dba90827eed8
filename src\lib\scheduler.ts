import { prisma } from '@/lib/prisma';
import { generateAllDailyReadings } from '@/lib/gemini';

class DailyReadingsScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastGeneratedDate: string | null = null;

  constructor() {
    this.start();
  }

  start() {
    if (this.isRunning) {
      console.log('📅 Daily readings scheduler is already running');
      return;
    }

    console.log('🚀 Starting daily readings scheduler...');
    this.isRunning = true;

    // Check immediately on startup
    this.checkAndGenerateReadings();

    // Then check every hour
    this.intervalId = setInterval(() => {
      this.checkAndGenerateReadings();
    }, 60 * 60 * 1000); // Check every hour

    console.log('✅ Daily readings scheduler started - checking every hour');
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('🛑 Daily readings scheduler stopped');
  }

  private async checkAndGenerateReadings() {
    try {
      const today = new Date().toISOString().split('T')[0];
      const currentHour = new Date().getHours();

      // Only generate readings between 12 AM and 6 AM (when most users are asleep)
      // and only once per day
      if (currentHour >= 6 && this.lastGeneratedDate === today) {
        return;
      }

      console.log(`🕐 Checking daily readings for ${today} at ${new Date().toLocaleTimeString()}`);

      // Check if readings already exist for today
      const existingReadings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(today),
          language: 'en'
        }
      });

      if (existingReadings.length === 12) {
        console.log(`✅ Daily readings already exist for ${today} (${existingReadings.length} readings)`);
        this.lastGeneratedDate = today;
        return;
      }

      console.log(`🤖 Generating daily zodiac readings for ${today}...`);
      console.log(`📊 Found ${existingReadings.length}/12 existing readings`);

      // Generate new readings using Gemini API
      const newReadings = await generateAllDailyReadings(today);
      console.log(`🎯 Generated ${newReadings.length} new readings from Gemini API`);

      // Save readings to database
      const savedReadings = [];
      for (const reading of newReadings) {
        try {
          const saved = await prisma.dailyZodiacReading.upsert({
            where: {
              zodiacSign_date_language: {
                zodiacSign: reading.zodiacSign,
                date: new Date(today),
                language: 'en'
              }
            },
            update: {
              generalReading: reading.generalReading,
              loveReading: reading.loveReading,
              careerReading: reading.careerReading,
              healthReading: reading.healthReading,
              luckyNumber: reading.luckyNumber,
              luckyColor: reading.luckyColor,
              luckyTime: reading.luckyTime,
              luckyGem: reading.luckyGem,
              advice: reading.advice,
              mood: reading.mood,
              compatibility: reading.compatibility,
              updatedAt: new Date()
            },
            create: {
              zodiacSign: reading.zodiacSign,
              date: new Date(today),
              generalReading: reading.generalReading,
              loveReading: reading.loveReading,
              careerReading: reading.careerReading,
              healthReading: reading.healthReading,
              luckyNumber: reading.luckyNumber,
              luckyColor: reading.luckyColor,
              luckyTime: reading.luckyTime,
              luckyGem: reading.luckyGem,
              advice: reading.advice,
              mood: reading.mood,
              compatibility: reading.compatibility,
              language: 'en'
            }
          });
          savedReadings.push(saved);
          console.log(`✅ Saved reading for ${reading.zodiacSign}`);
        } catch (error) {
          console.error(`❌ Error saving reading for ${reading.zodiacSign}:`, error);
        }
      }

      this.lastGeneratedDate = today;
      console.log(`🎉 Successfully generated and saved ${savedReadings.length} daily readings for ${today}`);

      // Clean up old readings (keep only last 30 days)
      await this.cleanupOldReadings();

    } catch (error) {
      console.error('❌ Error in daily readings scheduler:', error);
    }
  }

  private async cleanupOldReadings() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedCount = await prisma.dailyZodiacReading.deleteMany({
        where: {
          date: {
            lt: thirtyDaysAgo
          }
        }
      });

      if (deletedCount.count > 0) {
        console.log(`🧹 Cleaned up ${deletedCount.count} old daily readings`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up old readings:', error);
    }
  }

  // Method to manually trigger reading generation (for testing)
  async generateNow(date?: string) {
    const targetDate = date || new Date().toISOString().split('T')[0];
    console.log(`🔄 Manually generating readings for ${targetDate}...`);
    
    try {
      const readings = await generateAllDailyReadings(targetDate);
      
      const savedReadings = [];
      for (const reading of readings) {
        const saved = await prisma.dailyZodiacReading.upsert({
          where: {
            zodiacSign_date_language: {
              zodiacSign: reading.zodiacSign,
              date: new Date(targetDate),
              language: 'en'
            }
          },
          update: {
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: reading.compatibility,
            updatedAt: new Date()
          },
          create: {
            zodiacSign: reading.zodiacSign,
            date: new Date(targetDate),
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: reading.compatibility,
            language: 'en'
          }
        });
        savedReadings.push(saved);
      }

      console.log(`✅ Manually generated ${savedReadings.length} readings for ${targetDate}`);
      return savedReadings;
    } catch (error) {
      console.error('❌ Error in manual generation:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      lastGeneratedDate: this.lastGeneratedDate,
      nextCheck: this.intervalId ? 'Every hour' : 'Not scheduled'
    };
  }
}

// Create singleton instance
export const dailyReadingsScheduler = new DailyReadingsScheduler();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 Received SIGTERM, stopping scheduler...');
  dailyReadingsScheduler.stop();
});

process.on('SIGINT', () => {
  console.log('🔄 Received SIGINT, stopping scheduler...');
  dailyReadingsScheduler.stop();
});
