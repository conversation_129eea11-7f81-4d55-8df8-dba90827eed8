'use client';

import { useState, useEffect } from 'react';
import { Settings, Save, Loader2 } from 'lucide-react';
import { LanguageCode } from '@/types';

interface SystemSettingsData {
  id: string;
  defaultLanguage: LanguageCode;
  createdAt: string;
  updatedAt: string;
}

export default function SystemSettings() {
  const [settings, setSettings] = useState<SystemSettingsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setSettings(data.data);
      } else {
        setError(data.error || 'Failed to load settings');
      }
    } catch (err) {
      console.error('Settings fetch error:', err);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          defaultLanguage: settings.defaultLanguage
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setSettings(data.data);
        setSuccess('Settings saved successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.error || 'Failed to save settings');
      }
    } catch (err) {
      console.error('Settings save error:', err);
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleLanguageChange = (newLanguage: LanguageCode) => {
    if (settings) {
      setSettings({
        ...settings,
        defaultLanguage: newLanguage
      });
    }
  };

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="animate-spin text-white" size={32} />
          <span className="ml-3 text-white">Loading settings...</span>
        </div>
      </div>
    );
  }

  if (error && !settings) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="text-center py-8">
          <div className="text-red-400 mb-4">⚠️ Error loading settings</div>
          <p className="text-gray-300 mb-4">{error}</p>
          <button
            onClick={fetchSettings}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="flex items-center mb-6">
          <Settings className="text-purple-400 mr-3" size={24} />
          <h2 className="text-xl font-bold text-white">System Settings</h2>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <p className="text-red-200">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-6">
            <p className="text-green-200">{success}</p>
          </div>
        )}

        <div className="space-y-6">
          {/* Default Language Setting */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-3">
              Default Language for New Users
            </label>
            <p className="text-gray-400 text-sm mb-4">
              This language will be automatically assigned to new users when they are created.
              Users can change their language preference later.
            </p>
            
            <div className="grid grid-cols-2 gap-4 max-w-md">
              <button
                onClick={() => handleLanguageChange('en')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  settings?.defaultLanguage === 'en'
                    ? 'border-purple-500 bg-purple-500/20 text-white'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">🇺🇸</div>
                  <div className="font-medium">English</div>
                </div>
              </button>

              <button
                onClick={() => handleLanguageChange('si')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  settings?.defaultLanguage === 'si'
                    ? 'border-purple-500 bg-purple-500/20 text-white'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">🇱🇰</div>
                  <div className="font-medium">සිංහල</div>
                </div>
              </button>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4 border-t border-white/20">
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white px-6 py-2 rounded-lg transition-colors"
            >
              {saving ? (
                <>
                  <Loader2 className="animate-spin mr-2" size={16} />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2" size={16} />
                  Save Settings
                </>
              )}
            </button>
          </div>
        </div>

        {settings && (
          <div className="mt-6 pt-4 border-t border-white/20">
            <p className="text-gray-400 text-sm">
              Last updated: {new Date(settings.updatedAt).toLocaleString()}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
