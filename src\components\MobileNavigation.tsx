'use client';

import { useState, useEffect } from 'react';
import { Menu, X, Home, Star, Calendar, Clock, LogOut, BookOpen } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useUITranslation();

  const navigationItems = [
    { id: 'horoscope', label: t('horoscope'), icon: BookOpen },
    { id: 'guide', label: t('daily_guide'), icon: Clock }
  ];

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);
    setIsOpen(false);
  };

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <>
      {/* Mobile Menu Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          {isOpen ? <X size={20} className="text-white" /> : <Menu size={20} className="text-white" />}
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <>
          {/* Backdrop with blur */}
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[9998] md:hidden transition-opacity duration-300"
            onClick={() => setIsOpen(false)}
          />

          {/* Side Menu Drawer */}
          <div
            className={`fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 shadow-2xl z-[9999] md:hidden transform transition-transform duration-300 ease-out ${
              isOpen ? 'translate-x-0' : 'translate-x-full'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Elegant Header */}
            <div className="relative bg-gradient-to-r from-purple-800 to-indigo-800 px-6 py-6 border-b border-white/10">
              <div className="absolute inset-0 bg-black/20"></div>
              <div className="relative flex items-center justify-between">
                <div>
                  <h2 className="text-white font-bold text-xl tracking-wide">
                    {t('navigation')}
                  </h2>
                  <p className="text-purple-200 text-sm mt-1 font-light">
                    Explore your cosmic journey
                  </p>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-white/80 hover:text-white transition-all duration-200 p-2 hover:bg-white/10 rounded-full hover:rotate-90 transform"
                >
                  <X size={22} strokeWidth={2} />
                </button>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="px-4 py-6 space-y-2">
              {navigationItems.map(({ id, label, icon: Icon }, index) => (
                <button
                  key={id}
                  onClick={() => handleTabChange(id)}
                  className={`group w-full flex items-center space-x-4 px-4 py-4 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] ${
                    activeTab === id
                      ? 'bg-gradient-to-r from-white to-purple-50 text-purple-900 shadow-lg border border-purple-200'
                      : 'bg-white/5 text-white hover:bg-white/10 border border-white/10 hover:border-white/20'
                  }`}
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animation: isOpen ? 'slideInRight 0.4s ease-out forwards' : 'none'
                  }}
                >
                  <div className={`p-2 rounded-xl transition-all duration-300 ${
                    activeTab === id
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-white/10 text-white group-hover:bg-white/20'
                  }`}>
                    <Icon size={20} strokeWidth={2} />
                  </div>
                  <div className="flex-1 text-left">
                    <span className="font-semibold text-base tracking-wide">{label}</span>
                    <p className={`text-xs mt-0.5 ${
                      activeTab === id ? 'text-purple-600' : 'text-white/60'
                    }`}>
                      {id === 'horoscope' ? 'Your daily predictions' : 'Cosmic guidance today'}
                    </p>
                  </div>
                  <div className={`transition-transform duration-300 ${
                    activeTab === id ? 'rotate-0' : 'rotate-45 group-hover:rotate-0'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      activeTab === id ? 'bg-purple-500' : 'bg-white/40'
                    }`}></div>
                  </div>
                </button>
              ))}
            </div>

            {/* Elegant Logout Section */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-slate-900 to-transparent">
              <button
                onClick={onLogout}
                className="group w-full flex items-center space-x-4 px-4 py-4 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg border border-red-500/30"
              >
                <div className="p-2 bg-white/20 rounded-xl group-hover:bg-white/30 transition-all duration-300">
                  <LogOut size={20} strokeWidth={2} />
                </div>
                <div className="flex-1 text-left">
                  <span className="font-semibold text-base tracking-wide">{t('logout')}</span>
                  <p className="text-red-100 text-xs mt-0.5">Sign out securely</p>
                </div>
                <div className="transition-transform duration-300 group-hover:translate-x-1">
                  <div className="w-2 h-2 rounded-full bg-white/60"></div>
                </div>
              </button>
            </div>
          </div>
        </>
      )}


    </>
  );
}
