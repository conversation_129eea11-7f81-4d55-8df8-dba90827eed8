'use client';

import { useState, useEffect } from 'react';
import { Menu, X, LogOut } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useUITranslation();

  const handleLogout = () => {
    onLogout?.();
    setIsOpen(false);
  };

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <>
      {/* Mobile Menu Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          {isOpen ? <X size={20} className="text-white" /> : <Menu size={20} className="text-white" />}
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9998] md:hidden transition-all duration-300"
            onClick={() => setIsOpen(false)}
          />

          {/* Clean Side Menu */}
          <div
            className={`fixed top-0 right-0 h-full w-72 bg-white shadow-2xl z-[9999] md:hidden transform transition-all duration-300 ease-out ${
              isOpen ? 'translate-x-0' : 'translate-x-full'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Simple Header */}
            <div className="bg-gray-50 px-6 py-5 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-gray-900 font-semibold text-lg">
                  Menu
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Menu Content - Centered */}
            <div className="flex-1 flex items-center justify-center px-6">
              <button
                onClick={handleLogout}
                className="group flex items-center space-x-3 px-6 py-4 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <LogOut size={20} />
                <span className="font-medium text-base">{t('logout')}</span>
              </button>
            </div>
          </div>
        </>
      )}


    </>
  );
}
