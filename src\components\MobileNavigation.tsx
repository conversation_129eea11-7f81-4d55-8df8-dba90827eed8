'use client';

import { LogOut } from 'lucide-react';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const handleLogout = () => {
    onLogout?.();
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          {isOpen ? <X size={20} className="text-white" /> : <Menu size={20} className="text-white" />}
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <>
          {/* Backdrop with Blur */}
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-md z-[9998] md:hidden transition-all duration-250"
            onClick={() => setIsOpen(false)}
          />

          {/* Professional Side Drawer */}
          <div
            className={`fixed top-0 right-0 h-full w-[280px] bg-white shadow-xl z-[9999] md:hidden transform transition-transform duration-250 ease-out ${
              isOpen ? 'translate-x-0' : 'translate-x-full'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-4 border-b border-gray-100">
              <div className="w-6"></div> {/* Spacer for centering */}
              <h2 className="text-gray-900 font-medium text-base">Menu</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={18} strokeWidth={2} />
              </button>
            </div>

            {/* Content */}
            <div className="px-4 py-8">
              <button
                onClick={handleLogout}
                className="w-full flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-150 group"
              >
                <div className="w-5 h-5 mr-3 text-gray-500 group-hover:text-red-500 transition-colors">
                  <LogOut size={18} strokeWidth={2} />
                </div>
                <span className="text-sm font-medium">{t('logout')}</span>
              </button>
            </div>
          </div>
        </>
      )}


    </>
  );
}
