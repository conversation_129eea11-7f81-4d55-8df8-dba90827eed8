'use client';

import { useState, useEffect } from 'react';
import { Menu, X, Home, Star, Calendar, Clock, LogOut, BookOpen } from 'lucide-react';
import TranslatedText from './TranslatedText';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);

  const navigationItems = [
    { id: 'horoscope', label: 'Horoscope', icon: BookOpen },
    { id: 'guide', label: 'Daily Guide', icon: Clock }
  ];

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);
    setIsOpen(false);
  };

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <>
      {/* Mobile Menu Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          {isOpen ? <X size={20} className="text-white" /> : <Menu size={20} className="text-white" />}
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[200] md:hidden"
          onClick={() => setIsOpen(false)}
        >
          <div
            className="absolute top-0 right-0 w-64 h-full bg-gradient-to-b from-purple-900 via-purple-800 to-indigo-900 shadow-2xl border-l border-white/20"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 h-full flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-white font-semibold text-base">
                  <TranslatedText text="Navigation" />
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-white transition-colors p-1"
                >
                  <X size={20} />
                </button>
              </div>

              <nav className="space-y-2 flex-1">
                {navigationItems.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => handleTabChange(id)}
                    className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors ${
                      activeTab === id
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-gray-300 hover:bg-white/10 hover:text-white'
                    }`}
                  >
                    <Icon size={20} />
                    <span className="font-medium text-sm"><TranslatedText text={label} /></span>
                  </button>
                ))}
              </nav>

              <div className="mt-auto pt-4 border-t border-white/10">
                <button
                  onClick={onLogout}
                  className="w-full flex items-center space-x-3 px-3 py-3 text-gray-300 hover:bg-red-500/20 hover:text-red-400 rounded-lg transition-colors"
                >
                  <LogOut size={20} />
                  <span className="font-medium text-sm"><TranslatedText text="Logout" /></span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </>
  );
}
