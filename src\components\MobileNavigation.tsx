'use client';

import { useState, useEffect } from 'react';
import { Menu, X, Home, Star, Calendar, Clock, LogOut, BookOpen } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useUITranslation();

  const navigationItems = [
    { id: 'horoscope', label: t('horoscope'), icon: BookOpen },
    { id: 'guide', label: t('daily_guide'), icon: Clock }
  ];

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);
    setIsOpen(false);
  };

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <>
      {/* Mobile Menu Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          {isOpen ? <X size={20} className="text-white" /> : <Menu size={20} className="text-white" />}
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black z-[9998] md:hidden"
            onClick={() => setIsOpen(false)}
          />

          {/* Menu Drawer */}
          <div
            className="fixed inset-0 bg-purple-900 z-[9999] md:hidden animate-in slide-in-from-right duration-300 ease-out"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="h-full w-full flex flex-col">
              {/* Menu Header */}
              <div className="bg-purple-800 border-b border-white/20 px-6 py-4 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h2 className="text-white font-bold text-lg">
                    {t('navigation')}
                  </h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-gray-300 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
                  >
                    <X size={24} />
                  </button>
                </div>
              </div>

              {/* Menu Content */}
              <div className="flex-1 px-6 py-6 bg-purple-900">
                <nav className="space-y-3">
                  {navigationItems.map(({ id, label, icon: Icon }) => (
                    <button
                      key={id}
                      onClick={() => handleTabChange(id)}
                      className={`w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 ${
                        activeTab === id
                          ? 'bg-white text-purple-900 shadow-lg border border-white/50'
                          : 'bg-purple-700 text-white hover:bg-purple-600 border border-purple-600'
                      }`}
                    >
                      <Icon size={24} />
                      <span className="font-semibold text-base">{label}</span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Menu Footer */}
              <div className="bg-purple-800 border-t border-white/20 px-6 py-4 flex-shrink-0">
                <button
                  onClick={onLogout}
                  className="w-full flex items-center space-x-4 px-4 py-4 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-200 border border-red-500/30"
                >
                  <LogOut size={24} />
                  <span className="font-semibold text-base">{t('logout')}</span>
                </button>
              </div>
            </div>
          </div>
        </>
      )}


    </>
  );
}
