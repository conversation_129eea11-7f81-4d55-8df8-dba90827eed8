'use client';

import { LogOut } from 'lucide-react';

interface MobileNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout?: () => void;
  className?: string;
}

export default function MobileNavigation({ activeTab, onTabChange, onLogout, className = '' }: MobileNavigationProps) {
  const handleLogout = () => {
    onLogout?.();
  };

  return (
    <>
      {/* Mobile Logout Button */}
      <div className={`md:hidden ${className}`}>
        <button
          onClick={handleLogout}
          className="flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
        >
          <LogOut size={20} className="text-white" />
        </button>
      </div>
    </>
  );
}
