'use client';

import React, { useState, useEffect } from 'react';
import { Camera, CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';

interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
}

export default function CameraDiagnostic() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [cameras, setCameras] = useState<MediaDeviceInfo[]>([]);

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);

    // Test 1: Check if we're in a secure context
    addResult({
      test: 'Secure Context',
      status: window.isSecureContext ? 'pass' : 'warning',
      message: window.isSecureContext ? 'Running in secure context (HTTPS/localhost)' : 'Not in secure context - camera may not work',
      details: window.isSecureContext ? 'Camera API requires HTTPS or localhost' : 'Try accessing via HTTPS or localhost'
    });

    // Test 2: Check MediaDevices API support
    const hasMediaDevices = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    addResult({
      test: 'MediaDevices API',
      status: hasMediaDevices ? 'pass' : 'fail',
      message: hasMediaDevices ? 'MediaDevices API is supported' : 'MediaDevices API not supported',
      details: hasMediaDevices ? 'Browser supports camera access' : 'Browser does not support camera access'
    });

    if (!hasMediaDevices) {
      setIsRunning(false);
      return;
    }

    // Test 3: Check permissions
    try {
      if (navigator.permissions && navigator.permissions.query) {
        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
        addResult({
          test: 'Camera Permission',
          status: permission.state === 'granted' ? 'pass' : permission.state === 'denied' ? 'fail' : 'warning',
          message: `Camera permission: ${permission.state}`,
          details: permission.state === 'granted' ? 'Camera access is allowed' : 
                   permission.state === 'denied' ? 'Camera access is denied - check browser settings' :
                   'Camera permission not yet requested'
        });
      } else {
        addResult({
          test: 'Permission API',
          status: 'warning',
          message: 'Permissions API not supported',
          details: 'Cannot check permission status - will try direct access'
        });
      }
    } catch (error) {
      addResult({
        test: 'Permission Check',
        status: 'warning',
        message: 'Could not check camera permission',
        details: `Error: ${error}`
      });
    }

    // Test 4: Enumerate devices
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      setCameras(videoDevices);
      
      addResult({
        test: 'Camera Detection',
        status: videoDevices.length > 0 ? 'pass' : 'fail',
        message: `Found ${videoDevices.length} camera(s)`,
        details: videoDevices.map(d => d.label || 'Unknown camera').join(', ') || 'No cameras detected'
      });
    } catch (error) {
      addResult({
        test: 'Device Enumeration',
        status: 'fail',
        message: 'Failed to enumerate devices',
        details: `Error: ${error}`
      });
    }

    // Test 5: Try to access camera
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const tracks = stream.getVideoTracks();
      
      addResult({
        test: 'Camera Access',
        status: 'pass',
        message: 'Successfully accessed camera',
        details: `Active track: ${tracks[0]?.label || 'Unknown'}`
      });

      // Clean up
      tracks.forEach(track => track.stop());
    } catch (error: any) {
      addResult({
        test: 'Camera Access',
        status: 'fail',
        message: 'Failed to access camera',
        details: `Error: ${error.name} - ${error.message}`
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return <CheckCircle className="text-green-500" size={16} />;
      case 'fail': return <XCircle className="text-red-500" size={16} />;
      case 'warning': return <AlertCircle className="text-yellow-500" size={16} />;
      case 'info': return <Info className="text-blue-500" size={16} />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return 'text-green-300';
      case 'fail': return 'text-red-300';
      case 'warning': return 'text-yellow-300';
      case 'info': return 'text-blue-300';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-800 rounded-lg">
      <div className="flex items-center gap-3 mb-6">
        <Camera className="text-purple-400" size={24} />
        <h2 className="text-xl font-bold text-white">Camera Diagnostic Tool</h2>
      </div>

      <button
        onClick={runDiagnostics}
        disabled={isRunning}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors mb-6 ${
          isRunning 
            ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
            : 'bg-purple-600 hover:bg-purple-700 text-white'
        }`}
      >
        {isRunning ? 'Running Diagnostics...' : 'Run Camera Diagnostics'}
      </button>

      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white mb-4">Diagnostic Results</h3>
          
          {results.map((result, index) => (
            <div key={index} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                {getStatusIcon(result.status)}
                <span className="font-medium text-white">{result.test}</span>
              </div>
              
              <p className={`text-sm ${getStatusColor(result.status)} mb-1`}>
                {result.message}
              </p>
              
              {result.details && (
                <p className="text-xs text-gray-400">
                  {result.details}
                </p>
              )}
            </div>
          ))}

          {cameras.length > 0 && (
            <div className="bg-gray-700 rounded-lg p-4 mt-4">
              <h4 className="font-medium text-white mb-2">Available Cameras</h4>
              <ul className="space-y-1">
                {cameras.map((camera, index) => (
                  <li key={camera.deviceId} className="text-sm text-gray-300">
                    {index + 1}. {camera.label || `Camera ${index + 1}`}
                    <span className="text-gray-500 ml-2">({camera.deviceId.substring(0, 8)}...)</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <h4 className="font-medium text-blue-300 mb-2">Troubleshooting Tips</h4>
        <ul className="text-sm text-blue-200 space-y-1">
          <li>• Make sure you're accessing the site via HTTPS or localhost</li>
          <li>• Check if another application is using your camera</li>
          <li>• Try refreshing the page and allowing camera access</li>
          <li>• Check your browser's camera permissions in settings</li>
          <li>• Try using a different browser (Chrome, Firefox, Safari)</li>
        </ul>
      </div>
    </div>
  );
}
