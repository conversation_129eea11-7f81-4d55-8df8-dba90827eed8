import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyPassword, generateAdminToken } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Find admin by email
    const admin = await prisma.admin.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (!admin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid credentials'
      }, { status: 401 });
    }

    // Check if admin is active
    if (!admin.isActive) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Account is deactivated'
      }, { status: 401 });
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, admin.password);
    if (!isValidPassword) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid credentials'
      }, { status: 401 });
    }

    // Update last login
    await prisma.admin.update({
      where: { id: admin.id },
      data: { lastLogin: new Date() }
    });

    // Generate JWT token
    const token = generateAdminToken({
      adminId: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role as 'admin' | 'super_admin'
    });

    // Create response with token
    const response = NextResponse.json<ApiResponse<{
      admin: {
        id: string;
        email: string;
        name: string;
        role: string;
      };
      token: string;
    }>>({
      success: true,
      data: {
        admin: {
          id: admin.id,
          email: admin.email,
          name: admin.name,
          role: admin.role
        },
        token
      },
      message: 'Login successful'
    });

    // Set HTTP-only cookie for additional security
    response.cookies.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    });

    return response;

  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
