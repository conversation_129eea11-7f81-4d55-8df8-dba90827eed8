'use client';

import { useState } from 'react';
import { Star, MapPin, Calendar, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import TranslatedText from './TranslatedText';

interface BirthChartProps {
  birthChart: {
    id: string;
    ascendant: string;
    moonSign: string;
    sunSign: string;
    planetPositions: any[];
    housePositions: any[];
    aspects: any[];
    nakshatras: any[];
    dashas: any[];
    generalReading: string;
    strengthsWeaknesses: string;
    careerGuidance: string;
    relationshipGuidance: string;
    healthGuidance: string;
    readingsEn?: any;
    readingsSi?: any;
    calculatedAt: string;
    user?: {
      name: string;
      birthDate: string;
      birthTime?: string;
      birthPlace?: string;
    };
  };
  className?: string;
}

export default function BirthChart({ birthChart, className = '' }: BirthChartProps) {
  const [activeSection, setActiveSection] = useState<string>('general');
  const [showPlanetDetails, setShowPlanetDetails] = useState(false);

  const sections = [
    { id: 'general', label: 'General Reading', icon: Star },
    { id: 'strengths', label: 'Strengths & Growth', icon: Star },
    { id: 'career', label: 'Career Guidance', icon: Star },
    { id: 'relationships', label: 'Relationships', icon: Star },
    { id: 'health', label: 'Health & Wellness', icon: Star }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return (
          <div className="space-y-4">
            <p className="text-gray-200 leading-relaxed">
              <TranslatedText text={birthChart.generalReading} />
            </p>
          </div>
        );
      case 'strengths':
        return (
          <div className="space-y-4">
            <div className="text-gray-200 leading-relaxed whitespace-pre-line">
              <TranslatedText text={birthChart.strengthsWeaknesses} />
            </div>
          </div>
        );
      case 'career':
        return (
          <div className="space-y-4">
            <div className="text-gray-200 leading-relaxed whitespace-pre-line">
              <TranslatedText text={birthChart.careerGuidance} />
            </div>
          </div>
        );
      case 'relationships':
        return (
          <div className="space-y-4">
            <div className="text-gray-200 leading-relaxed whitespace-pre-line">
              <TranslatedText text={birthChart.relationshipGuidance} />
            </div>
          </div>
        );
      case 'health':
        return (
          <div className="space-y-4">
            <div className="text-gray-200 leading-relaxed whitespace-pre-line">
              <TranslatedText text={birthChart.healthGuidance} />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Star className="mr-2 text-yellow-400" size={24} />
            <TranslatedText text="Your Birth Chart (Handahana)" />
          </h2>
        </div>

        {/* Birth Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-sm text-gray-400 mb-1">
              <TranslatedText text="Ascendant (Rising Sign)" />
            </div>
            <div className="text-lg font-semibold text-white">{birthChart.ascendant}</div>
          </div>
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-sm text-gray-400 mb-1">
              <TranslatedText text="Moon Sign" />
            </div>
            <div className="text-lg font-semibold text-white">{birthChart.moonSign}</div>
          </div>
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-sm text-gray-400 mb-1">
              <TranslatedText text="Sun Sign" />
            </div>
            <div className="text-lg font-semibold text-white">{birthChart.sunSign}</div>
          </div>
        </div>

        {/* Birth Info */}
        {birthChart.user && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
            {birthChart.user.birthDate && (
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                {new Date(birthChart.user.birthDate).toLocaleDateString()}
              </div>
            )}
            {birthChart.user.birthTime && (
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                {birthChart.user.birthTime}
              </div>
            )}
            {birthChart.user.birthPlace && (
              <div className="flex items-center">
                <MapPin size={16} className="mr-1" />
                {birthChart.user.birthPlace}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="px-6 py-4 border-b border-white/10">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                activeSection === section.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-white/5 text-gray-300 hover:bg-white/10'
              }`}
            >
              <TranslatedText text={section.label} />
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {renderContent()}
      </div>

      {/* Planetary Positions (Collapsible) */}
      <div className="border-t border-white/10">
        <button
          onClick={() => setShowPlanetDetails(!showPlanetDetails)}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/5 transition-colors"
        >
          <span className="text-white font-medium">
            <TranslatedText text="Planetary Positions" />
          </span>
          {showPlanetDetails ? (
            <ChevronUp className="text-gray-400" size={20} />
          ) : (
            <ChevronDown className="text-gray-400" size={20} />
          )}
        </button>
        
        {showPlanetDetails && (
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {birthChart.planetPositions.map((planet: any, index: number) => (
                <div key={index} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{planet.name}</span>
                    {planet.isRetrograde && (
                      <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded">
                        <TranslatedText text="Retrograde" />
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-300">
                    <div><TranslatedText text="Sign" />: {planet.sign}</div>
                    <div><TranslatedText text="House" />: {planet.house}</div>
                    <div><TranslatedText text="Nakshatra" />: {planet.nakshatra}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-white/10 text-center">
        <p className="text-xs text-gray-400">
          <TranslatedText text="Calculated using Vedic Astrology principles" /> • 
          {' '}{new Date(birthChart.calculatedAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}
