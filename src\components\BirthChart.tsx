'use client';

import { useState, useEffect } from 'react';
import { MapPin, Calendar, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import VedicChart from './VedicChart';
import AstrologicalTables from './AstrologicalTables';
import {
  calculateEnhancedBirthChart,
  BirthChartData,
  BirthDetails
} from '@/lib/astrology';

interface BirthChartProps {
  birthChart: {
    id: string;
    ascendant: string;
    moonSign: string;
    sunSign: string;
    planetPositions: any[];
    housePositions: any[];
    aspects: any[];
    nakshatras: any[];
    dashas: any[];
    // Enhanced Vedic Chart Data
    lagnaChart?: any;
    navamsaChart?: any;
    chandraChart?: any;
    karakTable?: any;
    avasthaTable?: any;
    planetaryDetails?: any;
    vimshottariDasha?: any;
    ashtakavarga?: any;
    calculatedAt: string;
    user?: {
      name: string;
      birthDate: string;
      birthTime?: string;
      birthPlace?: string;
    };
  };
  className?: string;
}

export default function BirthChart({ birthChart, className = '' }: BirthChartProps) {
  const [showPlanetDetails, setShowPlanetDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'charts' | 'tables'>('charts');

  // Use the enhanced chart data from the birth chart if available
  const enhancedChart: BirthChartData = {
    ascendant: birthChart.ascendant,
    moonSign: birthChart.moonSign,
    sunSign: birthChart.sunSign,
    planets: birthChart.planetPositions || [],
    houses: birthChart.housePositions || [],
    aspects: birthChart.aspects || [],
    nakshatras: birthChart.nakshatras || [],
    dashas: birthChart.dashas || [],
    lagnaChart: birthChart.lagnaChart,
    navamsaChart: birthChart.navamsaChart,
    chandraChart: birthChart.chandraChart,
    karakTable: birthChart.karakTable,
    avasthaTable: birthChart.avasthaTable,
    planetaryDetails: birthChart.planetaryDetails,
    vimshottariDasha: birthChart.vimshottariDasha,
    ashtakavarga: birthChart.ashtakavarga
  };

  // Check if we have enhanced chart data
  const hasEnhancedData = enhancedChart.lagnaChart || enhancedChart.navamsaChart || enhancedChart.chandraChart;

  // Check if we have basic chart data
  const hasBasicData = birthChart.planetPositions && birthChart.planetPositions.length > 0;

  // Debug logging
  console.log('🔍 BirthChart component - Enhanced data check:', {
    hasEnhancedData,
    hasBasicData,
    lagnaChart: !!enhancedChart.lagnaChart,
    navamsaChart: !!enhancedChart.navamsaChart,
    chandraChart: !!enhancedChart.chandraChart,
    planetaryDetails: !!enhancedChart.planetaryDetails,
    karakTable: !!enhancedChart.karakTable,
    avasthaTable: !!enhancedChart.avasthaTable,
    vimshottariDasha: !!enhancedChart.vimshottariDasha,
    ashtakavarga: !!enhancedChart.ashtakavarga
  });

  const renderCharts = () => {
    if (!hasEnhancedData) {
      return (
        <div className="text-center py-12">
          <div className="bg-purple-800/20 rounded-lg p-6 border border-purple-500/20">
            <h3 className="text-lg font-semibold text-white mb-2">Enhanced Vedic Charts</h3>
            <p className="text-purple-200 mb-4">
              Enhanced Vedic chart calculations are not available for this birth chart.
            </p>
            <p className="text-sm text-purple-300 mb-4">
              This birth chart was generated with the basic system. The enhanced Vedic charts (Lagna, Navamsa, Chandra) are not available.
            </p>
            <div className="text-xs text-purple-400 bg-purple-900/30 rounded p-3">
              <p className="mb-1">Debug Info:</p>
              <p>Lagna Chart: {enhancedChart.lagnaChart ? '✅ Available' : '❌ Missing'}</p>
              <p>Navamsa Chart: {enhancedChart.navamsaChart ? '✅ Available' : '❌ Missing'}</p>
              <p>Chandra Chart: {enhancedChart.chandraChart ? '✅ Available' : '❌ Missing'}</p>
              <p>Planet Positions: {hasBasicData ? '✅ Available' : '❌ Missing'}</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-8">
        {/* Main Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Lagna Chart (D1) */}
          {enhancedChart.lagnaChart && (
            <VedicChart
              chartData={enhancedChart.lagnaChart}
              title="Lagna Chart"
              className="col-span-1"
            />
          )}

          {/* Navamsa Chart (D9) */}
          {enhancedChart.navamsaChart && (
            <VedicChart
              chartData={enhancedChart.navamsaChart}
              title="Navamsa Chart"
              className="col-span-1"
            />
          )}
        </div>

        {/* Chandra Chart */}
        <div className="flex justify-center">
          {enhancedChart.chandraChart && (
            <VedicChart
              chartData={enhancedChart.chandraChart}
              title="Chandra Chart"
              className="max-w-md"
            />
          )}
        </div>
      </div>
    );
  };

  const renderTables = () => {
    if (!hasEnhancedData) {
      return (
        <div className="text-center py-12">
          <div className="bg-purple-800/20 rounded-lg p-6 border border-purple-500/20">
            <h3 className="text-lg font-semibold text-white mb-2">Astrological Data Tables</h3>
            <p className="text-purple-200 mb-4">
              Enhanced astrological data tables are not available for this birth chart.
            </p>
            <p className="text-sm text-purple-300 mb-4">
              This birth chart was generated with the basic system. The enhanced astrological tables (Karaka, Avastha, Dasha) are not available.
            </p>
            <div className="text-xs text-purple-400 bg-purple-900/30 rounded p-3">
              <p className="mb-1">Debug Info:</p>
              <p>Karaka Table: {enhancedChart.karakTable ? '✅ Available' : '❌ Missing'}</p>
              <p>Avastha Table: {enhancedChart.avasthaTable ? '✅ Available' : '❌ Missing'}</p>
              <p>Planetary Details: {enhancedChart.planetaryDetails ? '✅ Available' : '❌ Missing'}</p>
              <p>Vimshottari Dasha: {enhancedChart.vimshottariDasha ? '✅ Available' : '❌ Missing'}</p>
              <p>Ashtakavarga: {enhancedChart.ashtakavarga ? '✅ Available' : '❌ Missing'}</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <AstrologicalTables
        karakTable={enhancedChart.karakTable}
        avasthaTable={enhancedChart.avasthaTable}
        planetaryDetails={enhancedChart.planetaryDetails}
        vimshottariDasha={enhancedChart.vimshottariDasha}
        ashtakavarga={enhancedChart.ashtakavarga}
      />
    );
  };
  return (
    <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-purple-500/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white flex items-center">
            ⭐ Your Birth Chart (Handahana)
          </h2>
        </div>

        {/* Birth Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Ascendant (Rising Sign)</div>
            <div className="text-lg font-semibold text-white">{birthChart.ascendant}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Moon Sign</div>
            <div className="text-lg font-semibold text-white">{birthChart.moonSign}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Sun Sign</div>
            <div className="text-lg font-semibold text-white">{birthChart.sunSign}</div>
          </div>
        </div>

        {/* Birth Info */}
        {birthChart.user && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
            {birthChart.user.birthDate && (
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                {new Date(birthChart.user.birthDate).toLocaleDateString()}
              </div>
            )}
            {birthChart.user.birthTime && (
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                {birthChart.user.birthTime}
              </div>
            )}
            {birthChart.user.birthPlace && (
              <div className="flex items-center">
                <MapPin size={16} className="mr-1" />
                {birthChart.user.birthPlace}
              </div>
            )}
          </div>
        )}

        {/* Tab Navigation */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setActiveTab('charts')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              activeTab === 'charts'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            Vedic Charts
          </button>
          <button
            onClick={() => setActiveTab('tables')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              activeTab === 'tables'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            Astrological Data
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'charts' ? renderCharts() : renderTables()}
      </div>

      {/* Planetary Positions (Collapsible) */}
      <div className="border-t border-purple-500/20">
        <button
          onClick={() => setShowPlanetDetails(!showPlanetDetails)}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-purple-800/20 transition-colors"
        >
          <span className="text-white font-medium">Planetary Positions</span>
          {showPlanetDetails ? (
            <ChevronUp className="text-purple-400" size={20} />
          ) : (
            <ChevronDown className="text-purple-400" size={20} />
          )}
        </button>

        {showPlanetDetails && (
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {enhancedChart.planets.map((planet: any, index: number) => (
                <div key={index} className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{planet.name}</span>
                    {planet.retrograde && (
                      <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded">
                        Retrograde
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-purple-200">
                    <div>Sign: {planet.sign}</div>
                    <div>House: {planet.house}</div>
                    <div>Nakshatra: {planet.nakshatra}</div>
                    <div>Longitude: {planet.longitude.toFixed(2)}°</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-purple-500/20 text-center">
        <p className="text-xs text-purple-300">
          Calculated using Vedic Astrology principles •
          {' '}{new Date(birthChart.calculatedAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}
