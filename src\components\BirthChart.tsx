'use client';

import { useState, useEffect } from 'react';
import { MapPin, Calendar, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import Vedic<PERSON>hart from './VedicChart';
import AstrologicalTables from './AstrologicalTables';
import {
  calculateEnhancedBirthChart,
  BirthChartData,
  BirthDetails
} from '@/lib/astrology';

interface BirthChartProps {
  birthChart: {
    id: string;
    ascendant: string;
    moonSign: string;
    sunSign: string;
    planetPositions: any[];
    housePositions: any[];
    aspects: any[];
    nakshatras: any[];
    dashas: any[];
    calculatedAt: string;
    user?: {
      name: string;
      birthDate: string;
      birthTime?: string;
      birthPlace?: string;
    };
  };
  className?: string;
}

export default function BirthChart({ birthChart, className = '' }: BirthChartProps) {
  const [enhancedChart, setEnhancedChart] = useState<BirthChartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPlanetDetails, setShowPlanetDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'charts' | 'tables'>('charts');

  useEffect(() => {
    const loadEnhancedChart = async () => {
      if (!birthChart.user?.birthDate || !birthChart.user?.birthTime || !birthChart.user?.birthPlace) {
        setLoading(false);
        return;
      }

      try {
        const birthDetails: BirthDetails = {
          birthDate: new Date(birthChart.user.birthDate),
          birthTime: birthChart.user.birthTime,
          birthPlace: birthChart.user.birthPlace,
          latitude: 0, // These would come from geocoding in real implementation
          longitude: 0
        };

        const enhanced = await calculateEnhancedBirthChart(birthDetails);
        setEnhancedChart(enhanced);
      } catch (error) {
        console.error('Error calculating enhanced chart:', error);
      } finally {
        setLoading(false);
      }
    };

    loadEnhancedChart();
  }, [birthChart]);

  if (loading) {
    return (
      <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl p-8 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
          <span className="ml-4 text-white">Calculating Vedic Charts...</span>
        </div>
      </div>
    );
  }

  if (!enhancedChart) {
    return (
      <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl p-8 ${className}`}>
        <div className="text-center text-white">
          <p>Unable to calculate enhanced birth chart.</p>
          <p className="text-sm text-gray-300 mt-2">Please ensure birth date, time, and place are provided.</p>
        </div>
      </div>
    );
  }

  const renderCharts = () => {
    return (
      <div className="space-y-8">
        {/* Main Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Lagna Chart (D1) */}
          {enhancedChart.lagnaChart && (
            <VedicChart
              chartData={enhancedChart.lagnaChart}
              title="Lagna Chart"
              className="col-span-1"
            />
          )}

          {/* Navamsa Chart (D9) */}
          {enhancedChart.navamsaChart && (
            <VedicChart
              chartData={enhancedChart.navamsaChart}
              title="Navamsa Chart"
              className="col-span-1"
            />
          )}
        </div>

        {/* Chandra Chart */}
        <div className="flex justify-center">
          {enhancedChart.chandraChart && (
            <VedicChart
              chartData={enhancedChart.chandraChart}
              title="Chandra Chart"
              className="max-w-md"
            />
          )}
        </div>
      </div>
    );
  };

  const renderTables = () => {
    return (
      <AstrologicalTables
        karakTable={enhancedChart.karakTable}
        avasthaTable={enhancedChart.avasthaTable}
        planetaryDetails={enhancedChart.planetaryDetails}
        vimshottariDasha={enhancedChart.vimshottariDasha}
        ashtakavarga={enhancedChart.ashtakavarga}
      />
    );
  };
  return (
    <div className={`bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-purple-500/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white flex items-center">
            ⭐ Your Birth Chart (Handahana)
          </h2>
        </div>

        {/* Birth Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Ascendant (Rising Sign)</div>
            <div className="text-lg font-semibold text-white">{birthChart.ascendant}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Moon Sign</div>
            <div className="text-lg font-semibold text-white">{birthChart.moonSign}</div>
          </div>
          <div className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
            <div className="text-sm text-purple-300 mb-1">Sun Sign</div>
            <div className="text-lg font-semibold text-white">{birthChart.sunSign}</div>
          </div>
        </div>

        {/* Birth Info */}
        {birthChart.user && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
            {birthChart.user.birthDate && (
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                {new Date(birthChart.user.birthDate).toLocaleDateString()}
              </div>
            )}
            {birthChart.user.birthTime && (
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                {birthChart.user.birthTime}
              </div>
            )}
            {birthChart.user.birthPlace && (
              <div className="flex items-center">
                <MapPin size={16} className="mr-1" />
                {birthChart.user.birthPlace}
              </div>
            )}
          </div>
        )}

        {/* Tab Navigation */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setActiveTab('charts')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              activeTab === 'charts'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            Vedic Charts
          </button>
          <button
            onClick={() => setActiveTab('tables')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              activeTab === 'tables'
                ? 'bg-purple-600 text-white'
                : 'bg-purple-800/20 text-purple-200 hover:bg-purple-700/30'
            }`}
          >
            Astrological Data
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'charts' ? renderCharts() : renderTables()}
      </div>

      {/* Planetary Positions (Collapsible) */}
      <div className="border-t border-purple-500/20">
        <button
          onClick={() => setShowPlanetDetails(!showPlanetDetails)}
          className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-purple-800/20 transition-colors"
        >
          <span className="text-white font-medium">Planetary Positions</span>
          {showPlanetDetails ? (
            <ChevronUp className="text-purple-400" size={20} />
          ) : (
            <ChevronDown className="text-purple-400" size={20} />
          )}
        </button>

        {showPlanetDetails && (
          <div className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {enhancedChart.planets.map((planet: any, index: number) => (
                <div key={index} className="bg-purple-800/20 rounded-lg p-3 border border-purple-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{planet.name}</span>
                    {planet.retrograde && (
                      <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded">
                        Retrograde
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-purple-200">
                    <div>Sign: {planet.sign}</div>
                    <div>House: {planet.house}</div>
                    <div>Nakshatra: {planet.nakshatra}</div>
                    <div>Longitude: {planet.longitude.toFixed(2)}°</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-purple-500/20 text-center">
        <p className="text-xs text-purple-300">
          Calculated using Vedic Astrology principles •
          {' '}{new Date(birthChart.calculatedAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}
