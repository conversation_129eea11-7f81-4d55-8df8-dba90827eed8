'use client';

import { useState, useEffect } from 'react';
import { Horoscope, ZodiacSign } from '@/types';
import { Plus, Edit, Trash2, Calendar, Globe, Users, Bot, Clock } from 'lucide-react';
import { ZODIAC_SIGNS, ZODIAC_INFO } from '@/utils/zodiac';
import { useConfirmDialog } from '@/contexts/DialogContext';
import AddHoroscopeModal from './AddHoroscopeModal';
import DailyGuideMonitoring from './DailyGuideMonitoring';

export default function ContentManagement() {
  const [horoscopes, setHoroscopes] = useState<Horoscope[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedZodiac, setSelectedZodiac] = useState<ZodiacSign | 'all'>('all');
  const [selectedType, setSelectedType] = useState<'daily' | 'weekly' | 'monthly' | 'all'>('all');
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'si' | 'all'>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [userCounts, setUserCounts] = useState<Record<ZodiacSign, number>>({} as Record<ZodiacSign, number>);
  const [activeTab, setActiveTab] = useState<'daily-guides' | 'horoscopes'>('daily-guides');
  const { confirmDelete } = useConfirmDialog();

  useEffect(() => {
    fetchHoroscopes();
    fetchUserCounts();
  }, [selectedZodiac, selectedType, selectedLanguage]);

  const fetchHoroscopes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedZodiac !== 'all') params.append('zodiacSign', selectedZodiac);
      if (selectedType !== 'all') params.append('type', selectedType);
      if (selectedLanguage !== 'all') params.append('language', selectedLanguage);

      const response = await fetch(`/api/admin/horoscopes?${params}`);
      const data = await response.json();

      if (data.success) {
        // Filter out daily horoscopes since they're automated
        const filteredHoroscopes = data.data.filter((h: Horoscope) => h.type !== 'daily');
        setHoroscopes(filteredHoroscopes);
      }
    } catch (error) {
      console.error('Error fetching horoscopes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserCounts = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users/counts', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserCounts(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching user counts:', error);
    }
  };

  const handleAddHoroscope = (newHoroscope: Horoscope) => {
    setHoroscopes([newHoroscope, ...horoscopes]);
    fetchUserCounts(); // Refresh user counts
  };

  const handleDeleteHoroscope = async (id: string) => {
    const horoscope = horoscopes.find(h => h.id === id);
    const horoscopeName = horoscope ? `${horoscope.type} horoscope for ${horoscope.zodiacSign}` : 'this horoscope';

    const confirmed = await confirmDelete(horoscopeName);
    if (!confirmed) return;

    try {
      const response = await fetch(`/api/admin/horoscopes?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setHoroscopes(horoscopes.filter(h => h.id !== id));
      }
    } catch (error) {
      console.error('Error deleting horoscope:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Content Management</h2>
          <p className="text-gray-300">Manage automated daily guides and manual horoscope content</p>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('daily-guides')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'daily-guides'
              ? 'bg-purple-600 text-white'
              : 'text-gray-300 hover:text-white hover:bg-white/10'
          }`}
        >
          <Bot size={16} />
          <span>Automated Daily Guides</span>
        </button>
        <button
          onClick={() => setActiveTab('horoscopes')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'horoscopes'
              ? 'bg-purple-600 text-white'
              : 'text-gray-300 hover:text-white hover:bg-white/10'
          }`}
        >
          <Edit size={16} />
          <span>Manual Horoscopes</span>
        </button>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'daily-guides' ? (
        <DailyGuideMonitoring />
      ) : (
        <div className="space-y-6">
          {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-gray-300 text-sm mb-2">Zodiac Sign</label>
          <select
            value={selectedZodiac}
            onChange={(e) => setSelectedZodiac(e.target.value as ZodiacSign | 'all')}
            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            style={{ colorScheme: 'dark' }}
          >
            <option value="all" className="bg-gray-800 text-white">All Signs</option>
            {ZODIAC_SIGNS.map(sign => (
              <option key={sign} value={sign} className="bg-gray-800 text-white">
                {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-gray-300 text-sm mb-2">Type</label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as any)}
            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            style={{ colorScheme: 'dark' }}
          >
            <option value="all" className="bg-gray-800 text-white">All Types</option>
            <option value="weekly" className="bg-gray-800 text-white">Weekly</option>
            <option value="monthly" className="bg-gray-800 text-white">Monthly</option>
          </select>
          <p className="text-xs text-gray-400 mt-1">Daily guides are automated</p>
        </div>

        <div>
          <label className="block text-gray-300 text-sm mb-2">Language</label>
          <select
            value={selectedLanguage}
            onChange={(e) => setSelectedLanguage(e.target.value as any)}
            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            style={{ colorScheme: 'dark' }}
          >
            <option value="all" className="bg-gray-800 text-white">All Languages</option>
            <option value="en" className="bg-gray-800 text-white">English</option>
            <option value="si" className="bg-gray-800 text-white">සිංහල</option>
          </select>
        </div>

        <div className="flex items-end">
          <button 
            onClick={() => setShowAddModal(true)}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <Plus size={16} />
            <span>Add Horoscope</span>
          </button>
        </div>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {horoscopes.map((horoscope) => (
          <div key={horoscope.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">{horoscope.zodiacSign && ZODIAC_INFO[horoscope.zodiacSign] ? ZODIAC_INFO[horoscope.zodiacSign].symbol : '⭐'}</div>
                <div>
                  <h3 className="text-white font-semibold">
                    {horoscope.zodiacSign && ZODIAC_INFO[horoscope.zodiacSign] ? ZODIAC_INFO[horoscope.zodiacSign].name : 'Unknown'}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      horoscope.type === 'daily' ? 'bg-green-500/20 text-green-300' :
                      horoscope.type === 'weekly' ? 'bg-blue-500/20 text-blue-300' :
                      'bg-purple-500/20 text-purple-300'
                    }`}>
                      {horoscope.type}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      horoscope.language === 'en' 
                        ? 'bg-blue-500/20 text-blue-300' 
                        : 'bg-green-500/20 text-green-300'
                    }`}>
                      {horoscope.language === 'en' ? 'EN' : 'SI'}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button className="text-purple-400 hover:text-purple-300 p-1" title="Edit">
                  <Edit size={16} />
                </button>
                <button 
                  onClick={() => handleDeleteHoroscope(horoscope.id)}
                  className="text-red-400 hover:text-red-300 p-1"
                  title="Delete"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            <p className="text-gray-300 text-sm mb-4 line-clamp-3">
              {horoscope.content}
            </p>

            <div className="flex items-center justify-between text-xs text-gray-400">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Calendar size={12} />
                  <span>{new Date(horoscope.date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users size={12} />
                  <span>{userCounts[horoscope.zodiacSign] || 0} users</span>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <Globe size={12} />
                <span>{horoscope.language === 'en' ? 'English' : 'සිංහල'}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {horoscopes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">📝</div>
          <h3 className="text-white font-semibold mb-2">No horoscopes found</h3>
          <p className="text-gray-400 mb-4">
            No horoscopes match your current filters. Try adjusting your search criteria.
          </p>
          <button 
            onClick={() => setShowAddModal(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Add First Horoscope
          </button>
        </div>
      )}

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-white">{horoscopes.filter(h => h.type === 'weekly').length}</p>
              <p className="text-gray-400 text-sm">Weekly</p>
            </div>
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-white">{horoscopes.filter(h => h.type === 'monthly').length}</p>
              <p className="text-gray-400 text-sm">Monthly</p>
            </div>
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-white">{horoscopes.filter(h => h.language === 'si').length}</p>
              <p className="text-gray-400 text-sm">Sinhala</p>
            </div>
          </div>

          {/* Add Horoscope Modal */}
          <AddHoroscopeModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
            onAdd={handleAddHoroscope}
          />
        </div>
      )}
    </div>
  );
}
