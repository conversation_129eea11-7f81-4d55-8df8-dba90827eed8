import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  totalScans: number;
  totalHoroscopes: number;
  userGrowth: number;
  scanGrowth: number;
  topZodiacSigns: Array<{
    sign: string;
    count: number;
    percentage: number;
  }>;
  languageDistribution: Array<{
    language: string;
    count: number;
    percentage: number;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    const daysBack = range === '7d' ? 7 : range === '30d' ? 30 : 90;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    // Get basic stats
    const totalUsers = await prisma.user.count();
    const totalHoroscopes = await prisma.horoscope.count();
    
    // Get total scans
    const scanStats = await prisma.qrCodeMapping.aggregate({
      _sum: {
        scanCount: true
      }
    });
    const totalScans = scanStats._sum.scanCount || 0;

    // Get active users (users who have scanned their QR code at least once)
    const activeUsers = await prisma.qrCodeMapping.count({
      where: {
        scanCount: {
          gt: 0
        }
      }
    });

    // Calculate growth rates (simplified - comparing with previous period)
    const previousPeriodStart = new Date(startDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));
    
    const previousUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate
        }
      }
    });

    const currentUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: startDate
        }
      }
    });

    const userGrowth = previousUsers > 0 ? Math.round(((currentUsers - previousUsers) / previousUsers) * 100) : 0;

    // Get previous scans for growth calculation
    const previousScans = await prisma.qrCodeMapping.aggregate({
      where: {
        lastScanned: {
          gte: previousPeriodStart,
          lt: startDate
        }
      },
      _sum: {
        scanCount: true
      }
    });

    const currentScans = await prisma.qrCodeMapping.aggregate({
      where: {
        lastScanned: {
          gte: startDate
        }
      },
      _sum: {
        scanCount: true
      }
    });

    const prevScanCount = previousScans._sum.scanCount || 0;
    const currScanCount = currentScans._sum.scanCount || 0;
    const scanGrowth = prevScanCount > 0 ? Math.round(((currScanCount - prevScanCount) / prevScanCount) * 100) : 0;

    // Get zodiac sign distribution
    const zodiacStats = await prisma.user.groupBy({
      by: ['zodiacSign'],
      _count: {
        zodiacSign: true
      },
      orderBy: {
        _count: {
          zodiacSign: 'desc'
        }
      },
      take: 5
    });

    const topZodiacSigns = zodiacStats.map(stat => ({
      sign: stat.zodiacSign,
      count: stat._count.zodiacSign,
      percentage: Math.round((stat._count.zodiacSign / totalUsers) * 100)
    }));

    // Get language distribution
    const languageStats = await prisma.user.groupBy({
      by: ['languagePreference'],
      _count: {
        languagePreference: true
      }
    });

    const languageDistribution = languageStats.map(stat => ({
      language: stat.languagePreference,
      count: stat._count.languagePreference,
      percentage: Math.round((stat._count.languagePreference / totalUsers) * 100)
    }));

    const analytics: AnalyticsData = {
      totalUsers,
      activeUsers,
      totalScans,
      totalHoroscopes,
      userGrowth,
      scanGrowth,
      topZodiacSigns,
      languageDistribution
    };

    return NextResponse.json<ApiResponse<AnalyticsData>>({
      success: true,
      data: analytics,
      message: 'Analytics data retrieved successfully'
    });

  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
