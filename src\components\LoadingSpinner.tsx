'use client';

import { Loader2 } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function LoadingSpinner({
  message,
  size = 'md',
  className = ''
}: LoadingSpinnerProps) {
  const { t } = useUITranslation();
  const defaultMessage = message || t('loading_ellipsis');

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <Loader2 className={`${sizeClasses[size]} text-white animate-spin mb-4`} />
      {defaultMessage && (
        <p className="text-white text-center">{defaultMessage}</p>
      )}
    </div>
  );
}
