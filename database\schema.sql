-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate enum types
CREATE TYPE zodiac_sign AS ENUM (
  'aries', 'taurus', 'gemini', 'cancer',
  'leo', 'virgo', 'libra', 'scorpio',
  'sagittarius', 'capricorn', 'aquarius', 'pisces'
);

CREATE TYPE language_code AS ENUM ('en', 'si');
CREATE TYPE horoscope_type AS ENUM ('daily', 'weekly', 'monthly');

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE,
  name VARCHA<PERSON>(255) NOT NULL,
  zodiac_sign zodiac_sign NOT NULL,
  birth_date DATE NOT NULL,
  qr_token UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
  language_preference language_code DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- QR Code Mappings table
CREATE TABLE qr_code_mappings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  qr_token UUID UNIQUE NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_scanned TIMESTAMP WITH TIME ZONE,
  scan_count INTEGER DEFAULT 0
);

-- Horoscopes table
CREATE TABLE horoscopes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  zodiac_sign zodiac_sign NOT NULL,
  type horoscope_type NOT NULL,
  content TEXT NOT NULL,
  date DATE NOT NULL,
  language language_code DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(zodiac_sign, type, date, language)
);

-- Daily Guides table
CREATE TABLE daily_guides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  zodiac_sign zodiac_sign NOT NULL,
  date DATE NOT NULL,
  lucky_number INTEGER NOT NULL,
  lucky_color VARCHAR(50) NOT NULL,
  lucky_time VARCHAR(50) NOT NULL,
  advice TEXT NOT NULL,
  language language_code DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(zodiac_sign, date, language)
);

-- Translation Cache table
CREATE TABLE translation_cache (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  original_text TEXT NOT NULL,
  translated_text TEXT NOT NULL,
  source_language language_code NOT NULL,
  target_language language_code NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(original_text, source_language, target_language)
);

-- Create indexes for better performance
CREATE INDEX idx_users_qr_token ON users(qr_token);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_qr_mappings_token ON qr_code_mappings(qr_token);
CREATE INDEX idx_qr_mappings_user ON qr_code_mappings(user_id);
CREATE INDEX idx_horoscopes_zodiac_date ON horoscopes(zodiac_sign, date);
CREATE INDEX idx_horoscopes_type_date ON horoscopes(type, date);
CREATE INDEX idx_daily_guides_zodiac_date ON daily_guides(zodiac_sign, date);
CREATE INDEX idx_translation_cache_lookup ON translation_cache(original_text, source_language, target_language);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create QR mapping when user is created
CREATE OR REPLACE FUNCTION create_qr_mapping()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO qr_code_mappings (qr_token, user_id)
  VALUES (NEW.qr_token, NEW.id);
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-create QR mapping
CREATE TRIGGER create_qr_mapping_trigger
  AFTER INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION create_qr_mapping();

-- Row Level Security (RLS) Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_code_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE horoscopes ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_guides ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_cache ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid()::text = id::text);

-- QR mappings are accessible by the mapped user
CREATE POLICY "Users can view own QR mappings" ON qr_code_mappings
  FOR SELECT USING (user_id::text = auth.uid()::text);

-- Horoscopes and daily guides are publicly readable
CREATE POLICY "Horoscopes are publicly readable" ON horoscopes
  FOR SELECT USING (true);

CREATE POLICY "Daily guides are publicly readable" ON daily_guides
  FOR SELECT USING (true);

-- Translation cache is publicly readable
CREATE POLICY "Translation cache is publicly readable" ON translation_cache
  FOR SELECT USING (true);
