'use client';

import { AlertCircle, RefreshCw } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

export default function ErrorMessage({
  title,
  message,
  onRetry,
  className = ''
}: ErrorMessageProps) {
  const { t } = useUITranslation();
  const defaultTitle = title || t('something_went_wrong');

  return (
    <div className={`text-center max-w-md mx-auto p-6 ${className}`}>
      <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
      <h2 className="text-xl font-bold text-white mb-4">{defaultTitle}</h2>
      <p className="text-gray-300 mb-6">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto"
        >
          <RefreshCw size={16} />
          {t('try_again')}
        </button>
      )}
    </div>
  );
}
