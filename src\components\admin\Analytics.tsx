'use client';

import { useState, useEffect } from 'react';
import { BarChart3, Users, TrendingUp, Calendar, Globe, Clock, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAlertDialog } from '@/contexts/DialogContext';

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  totalScans: number;
  totalHoroscopes: number;
  userGrowth: number;
  scanGrowth: number;
  topZodiacSigns: Array<{
    sign: string;
    count: number;
    percentage: number;
  }>;
  languageDistribution: Array<{
    language: string;
    count: number;
    percentage: number;
  }>;
}

export default function Analytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [schedulerStatus, setSchedulerStatus] = useState<any>(null);
  const [generatingReadings, setGeneratingReadings] = useState(false);
  const { showSuccess, showError } = useAlertDialog();

  useEffect(() => {
    fetchAnalytics();
    fetchSchedulerStatus();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/analytics?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.data);
      } else {
        const errorText = await response.text();
        console.error('Failed to load analytics:', response.status, errorText);
        // Set default analytics data to prevent UI errors
        setAnalytics({
          totalUsers: 0,
          totalHoroscopes: 0,
          totalPersonalHoroscopes: 0,
          dailyReadings: 0,
          userGrowth: [],
          horoscopesByType: [],
          usersByZodiac: []
        });
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Set default analytics data to prevent UI errors
      setAnalytics({
        totalUsers: 0,
        totalHoroscopes: 0,
        totalPersonalHoroscopes: 0,
        dailyReadings: 0,
        userGrowth: [],
        horoscopesByType: [],
        usersByZodiac: []
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchSchedulerStatus = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/scheduler', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSchedulerStatus(data.data);
      }
    } catch (error) {
      console.error('Failed to load scheduler status:', error);
    }
  };

  const generateDailyReadings = async () => {
    try {
      setGeneratingReadings(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ action: 'generate' })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          showSuccess('Success', `Successfully generated ${data.data.count} daily readings!`);
          fetchSchedulerStatus(); // Refresh status
        } else {
          showError('Generation Failed', data.error || 'Unknown error occurred');
        }
      } else {
        const errorText = await response.text();
        console.error('Generate readings error:', response.status, errorText);
        if (response.status === 429) {
          showError('Rate Limited', 'Please wait before generating again to avoid API rate limits');
        } else {
          showError('Error', 'Failed to generate readings. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error generating readings:', error);
      alert('Error generating readings. Please try again.');
    } finally {
      setGeneratingReadings(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-white/10 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white/10 rounded-lg p-6 h-32"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Total Users</p>
              <p className="text-3xl font-bold text-white">{analytics?.totalUsers || 0}</p>
              <p className="text-green-400 text-sm">+{analytics?.userGrowth || 0}% growth</p>
            </div>
            <Users className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Active Users</p>
              <p className="text-3xl font-bold text-white">{analytics?.activeUsers || 0}</p>
              <p className="text-gray-400 text-sm">Users with scans</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Total Scans</p>
              <p className="text-3xl font-bold text-white">{analytics?.totalScans || 0}</p>
              <p className="text-green-400 text-sm">+{analytics?.scanGrowth || 0}% growth</p>
            </div>
            <BarChart3 className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Content Items</p>
              <p className="text-3xl font-bold text-white">{analytics?.totalHoroscopes || 0}</p>
              <p className="text-gray-400 text-sm">Horoscopes & guides</p>
            </div>
            <Calendar className="w-8 h-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Daily Readings Scheduler Status */}
      {schedulerStatus && (
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <Clock className="mr-2 text-blue-400" />
              Daily Readings Scheduler
            </h3>
            <button
              onClick={fetchSchedulerStatus}
              className="text-gray-400 hover:text-white transition-colors"
              title="Refresh Status"
            >
              <RefreshCw size={16} />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center mb-2">
                {schedulerStatus.scheduler.isRunning ? (
                  <CheckCircle className="text-green-400 mr-2" size={20} />
                ) : (
                  <AlertCircle className="text-red-400 mr-2" size={20} />
                )}
                <h4 className="text-white font-semibold">Status</h4>
              </div>
              <p className={`text-sm ${schedulerStatus.scheduler.isRunning ? 'text-green-300' : 'text-red-300'}`}>
                {schedulerStatus.scheduler.isRunning ? 'Running' : 'Stopped'}
              </p>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Calendar className="text-blue-400 mr-2" size={20} />
                <h4 className="text-white font-semibold">Last Generated</h4>
              </div>
              <p className="text-sm text-gray-300">
                {schedulerStatus.scheduler.lastGeneratedDate || 'Never'}
              </p>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Clock className="text-purple-400 mr-2" size={20} />
                <h4 className="text-white font-semibold">Next Check</h4>
              </div>
              <p className="text-sm text-gray-300">
                {schedulerStatus.scheduler.nextCheck}
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-300">
              <p>Server Time: {new Date(schedulerStatus.serverTime).toLocaleString()}</p>
            </div>
            <button
              onClick={generateDailyReadings}
              disabled={generatingReadings}
              className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              {generatingReadings ? (
                <>
                  <RefreshCw className="animate-spin" size={16} />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <RefreshCw size={16} />
                  <span>Generate Now</span>
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Charts and Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Zodiac Signs */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">Popular Zodiac Signs</h3>
          <div className="space-y-3">
            {analytics?.topZodiacSigns?.map((sign, index) => (
              <div key={sign.sign} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getZodiacSymbol(sign.sign)}</span>
                  <span className="text-white">{sign.sign}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-purple-500 h-2 rounded-full" 
                      style={{ width: `${sign.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-gray-300 text-sm w-12">{sign.count}</span>
                </div>
              </div>
            )) || (
              <p className="text-gray-400 text-center py-4">No data available</p>
            )}
          </div>
        </div>

        {/* Language Distribution */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">Language Preferences</h3>
          <div className="space-y-3">
            {analytics?.languageDistribution?.map((lang) => (
              <div key={lang.language} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Globe className="w-5 h-5 text-blue-400" />
                  <span className="text-white">{lang.language === 'en' ? 'English' : 'Sinhala'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${lang.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-gray-300 text-sm w-12">{lang.count}</span>
                </div>
              </div>
            )) || (
              <p className="text-gray-400 text-center py-4">No data available</p>
            )}
          </div>
        </div>
      </div>

      {/* Coming Soon Features */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
        <h3 className="text-xl font-bold text-white mb-4">Advanced Analytics</h3>
        <p className="text-gray-300 mb-6">
          More detailed charts, user behavior analysis, and engagement metrics coming soon.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-400">
          <div>📊 User Engagement Trends</div>
          <div>📈 Content Performance</div>
          <div>🎯 Conversion Analytics</div>
        </div>
      </div>
    </div>
  );
}

function getZodiacSymbol(sign: string): string {
  const symbols: { [key: string]: string } = {
    'Aries': '♈',
    'Taurus': '♉',
    'Gemini': '♊',
    'Cancer': '♋',
    'Leo': '♌',
    'Virgo': '♍',
    'Libra': '♎',
    'Scorpio': '♏',
    'Sagittarius': '♐',
    'Capricorn': '♑',
    'Aquarius': '♒',
    'Pisces': '♓'
  };
  return symbols[sign] || '⭐';
}
