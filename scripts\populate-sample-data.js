const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌟 Starting sample data population...');

  try {
    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('🧹 Clearing existing sample data...');
    await prisma.dailyZodiacReading.deleteMany({});
    await prisma.personalHoroscope.deleteMany({});
    await prisma.horoscope.deleteMany({});
    await prisma.qrCodeMapping.deleteMany({});
    await prisma.user.deleteMany({});

    // Create sample users
    console.log('👥 Creating sample users...');
    const users = await Promise.all([
      prisma.user.create({
        data: {
          name: '<PERSON>',
          zodiacSign: 'aries',
          birthDate: new Date('1990-04-15'),
          email: '<EMAIL>',
          languagePreference: 'en',
          qrToken: 'user123'
        }
      }),
      prisma.user.create({
        data: {
          name: '<PERSON>',
          zodiacSign: 'leo',
          birthDate: new Date('1985-08-10'),
          email: '<EMAIL>',
          languagePreference: 'en',
          qrToken: 'astro456'
        }
      }),
      prisma.user.create({
        data: {
          name: 'Kasun Perera',
          zodiacSign: 'scorpio',
          birthDate: new Date('1992-11-05'),
          email: '<EMAIL>',
          languagePreference: 'si',
          qrToken: 'cosmic789'
        }
      }),
      prisma.user.create({
        data: {
          name: 'Nimal Silva',
          zodiacSign: 'pisces',
          birthDate: new Date('1988-03-12'),
          email: '<EMAIL>',
          languagePreference: 'si',
          qrToken: 'test123'
        }
      })
    ]);

    console.log(`✅ Created ${users.length} sample users`);

    // Create QR code mappings
    console.log('🔗 Creating QR code mappings...');
    for (const user of users) {
      await prisma.qrCodeMapping.create({
        data: {
          qrToken: user.qrToken,
          userId: user.id,
          scanCount: Math.floor(Math.random() * 10)
        }
      });
    }

    // Create sample horoscopes for today
    console.log('🔮 Creating sample horoscopes...');
    const today = new Date();
    const horoscopeData = [
      { sign: 'aries', daily: 'Today brings new opportunities for growth and adventure. Trust your instincts and take bold steps forward.', weekly: 'This week focuses on career advancement and personal relationships. Balance is key to your success.', monthly: 'March brings transformation and new beginnings. Embrace change and let your natural leadership shine.' },
      { sign: 'leo', daily: 'Your creative energy is at its peak today. Express yourself boldly and share your talents with the world.', weekly: 'Focus on building stronger connections with loved ones. Your warmth and generosity will be appreciated.', monthly: 'This month highlights your natural charisma and leadership abilities. Step into the spotlight confidently.' },
      { sign: 'scorpio', daily: 'Deep insights and intuitive understanding guide you today. Trust your inner wisdom in all decisions.', weekly: 'Transformation and renewal are themes this week. Let go of what no longer serves your highest good.', monthly: 'Intense personal growth and spiritual awakening characterize this month. Embrace your authentic self.' },
      { sign: 'pisces', daily: 'Your compassionate nature brings healing to others today. Practice self-care while helping those in need.', weekly: 'Dreams and intuition provide valuable guidance this week. Pay attention to subtle signs and synchronicities.', monthly: 'Artistic and spiritual pursuits flourish this month. Connect with your creative and mystical side.' }
    ];

    for (const data of horoscopeData) {
      await Promise.all([
        prisma.horoscope.create({
          data: {
            zodiacSign: data.sign,
            type: 'daily',
            content: data.daily,
            date: today,
            language: 'en'
          }
        }),
        prisma.horoscope.create({
          data: {
            zodiacSign: data.sign,
            type: 'weekly',
            content: data.weekly,
            date: today,
            language: 'en'
          }
        }),
        prisma.horoscope.create({
          data: {
            zodiacSign: data.sign,
            type: 'monthly',
            content: data.monthly,
            date: today,
            language: 'en'
          }
        })
      ]);
    }

    // Create daily zodiac readings
    console.log('📅 Creating daily zodiac readings...');
    const dailyReadings = [
      {
        zodiacSign: 'aries',
        generalReading: 'Today brings new opportunities for growth and adventure. Trust your instincts and take bold steps forward.',
        loveReading: 'Romance is in the air for Aries today. Single Aries may meet someone special, while coupled Aries should plan something romantic.',
        careerReading: 'Your leadership skills shine at work today. Take initiative on important projects and your efforts will be recognized.',
        healthReading: 'High energy levels support physical activities. Consider starting a new fitness routine or outdoor adventure.',
        luckyNumber: 7,
        luckyColor: 'Red',
        luckyTime: '10:30 AM',
        luckyGem: 'Ruby',
        advice: 'Take initiative in important matters. Your leadership skills will be recognized today.',
        mood: 'Energetic',
        compatibility: 'Leo, Sagittarius'
      },
      {
        zodiacSign: 'leo',
        generalReading: 'Your creative energy is at its peak today. Express yourself boldly and share your talents with the world.',
        loveReading: 'Your charismatic nature attracts admirers today. Be open to new connections and express your feelings honestly.',
        careerReading: 'Creative projects and presentations go exceptionally well. Your natural leadership inspires your team to achieve great things.',
        healthReading: 'Focus on heart health and cardiovascular exercise. Your vitality is strong, but dont overexert yourself.',
        luckyNumber: 3,
        luckyColor: 'Gold',
        luckyTime: '2:15 PM',
        luckyGem: 'Citrine',
        advice: 'Express your creativity and share your ideas. Others will be inspired by your enthusiasm.',
        mood: 'Confident',
        compatibility: 'Aries, Gemini'
      },
      {
        zodiacSign: 'scorpio',
        generalReading: 'Deep insights and intuitive understanding guide you today. Trust your inner wisdom in all decisions.',
        loveReading: 'Intense emotional connections are possible today. Existing relationships deepen, and new ones may have profound significance.',
        careerReading: 'Research and investigation skills are highlighted. Dig deeper into projects to uncover hidden opportunities.',
        healthReading: 'Pay attention to your bodys signals. Detoxification and cleansing activities are particularly beneficial today.',
        luckyNumber: 9,
        luckyColor: 'Deep Purple',
        luckyTime: '8:45 PM',
        luckyGem: 'Obsidian',
        advice: 'Trust your intuition in making important decisions. Your inner wisdom is particularly strong today.',
        mood: 'Intense',
        compatibility: 'Cancer, Pisces'
      },
      {
        zodiacSign: 'pisces',
        generalReading: 'Your compassionate nature brings healing to others today. Practice self-care while helping those in need.',
        loveReading: 'Emotional sensitivity enhances romantic connections. Listen to your heart and trust your feelings about relationships.',
        careerReading: 'Creative and artistic work flourishes. Your imagination and intuition guide you to innovative solutions.',
        healthReading: 'Water-based activities and meditation support your well-being. Pay attention to your emotional and spiritual health.',
        luckyNumber: 12,
        luckyColor: 'Sea Blue',
        luckyTime: '6:20 AM',
        luckyGem: 'Aquamarine',
        advice: 'Practice meditation or spend time near water. Your spiritual connection is heightened today.',
        mood: 'Compassionate',
        compatibility: 'Scorpio, Cancer'
      }
    ];

    for (const reading of dailyReadings) {
      await prisma.dailyZodiacReading.create({
        data: {
          ...reading,
          date: today,
          language: 'en'
        }
      });
    }

    // Create sample personal horoscopes
    console.log('⭐ Creating personal horoscopes...');
    for (const user of users) {
      await prisma.personalHoroscope.create({
        data: {
          userId: user.id,
          title: `Special Message for ${user.name}`,
          content: `Dear ${user.name}, the stars have aligned to bring you a special message today. Your journey as a ${user.zodiacSign} is unique and filled with potential. Trust in your abilities and embrace the cosmic energy surrounding you.`,
          isActive: true,
          language: user.languagePreference
        }
      });
    }

    console.log('🎉 Sample data population completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Created ${users.length} users`);
    console.log(`- Created ${users.length} QR code mappings`);
    console.log(`- Created ${horoscopeData.length * 3} horoscopes`);
    console.log(`- Created ${dailyReadings.length} daily zodiac readings`);
    console.log(`- Created ${users.length} personal horoscopes`);
    console.log('\n🔗 Test QR Tokens:');
    users.forEach(user => {
      console.log(`- ${user.name} (${user.zodiacSign}): http://localhost:3000/auth?token=${user.qrToken}`);
    });

  } catch (error) {
    console.error('❌ Error populating sample data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
