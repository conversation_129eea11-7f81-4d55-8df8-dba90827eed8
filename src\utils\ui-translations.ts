// UI Text Translations
// This file contains all static UI text translations for the application

import { useLanguage } from '@/hooks/useLanguage';

export type UITextKey = 
  // Navigation and Tabs
  | 'horoscope'
  | 'daily_guide'
  | 'today_cosmic_guide'
  | 'general_reading'
  | 'love_relationships'
  | 'career_money'
  | 'health_wellness'
  | 'todays_lucky_elements'
  | 'lucky_number'
  | 'lucky_color'
  | 'lucky_time'
  | 'lucky_gem'
  | 'daily_advice'
  | 'compatible_signs_today'
  | 'welcome'
  | 'loading_cosmic_insights'
  | 'error_loading_dashboard'
  | 'configuration_error'
  | 'session_expire_warning'
  | 'settings'
  | 'filter'
  | 'add_user'
  | 'logout'
  | 'admin_user'
  | 'manage_users_content_analytics'
  | 'overview'
  | 'users'
  | 'content'
  | 'personal_horoscopes'
  | 'analytics'
  | 'search_users'
  | 'name'
  | 'email'
  | 'phone'
  | 'zodiac'
  | 'language'
  | 'scans'
  | 'last_active'
  | 'actions'
  | 'never'
  | 'showing_users'
  | 'previous'
  | 'next'
  | 'daily_guides'
  | 'horoscopes'
  | 'all_signs'
  | 'all_types'
  | 'all_languages'
  | 'english'
  | 'sinhala'
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'intense'
  | 'research_investigation'
  | 'pay_attention_body'
  | 'trust_intuition'
  | 'deep_insights'
  | 'intense_emotional_connections'
  | 'dig_deeper_projects'
  | 'detoxification_cleansing'
  // Dashboard Messages
  | 'no_personal_horoscope_available'
  | 'personalized_horoscope_content_message'
  // Landing Page
  | 'your_personal_horoscope_daily_guide'
  | 'discover_cosmic_destiny'
  | 'daily_horoscopes'
  | 'daily_horoscopes_description'
  | 'lucky_guidance'
  | 'lucky_guidance_description'
  | 'qr_access'
  | 'qr_access_description'
  | 'scan_qr_code'
  | 'upload_qr_image'
  | 'scan_your_qr_code'
  | 'qr_card_description'
  | 'qr_compatibility_tip'
  // QR Scanner
  | 'use_camera'
  | 'choose_file'
  | 'loading_scanner_library'
  | 'starting_camera'
  | 'try_scanning_again'
  | 'dismiss'
  | 'back_to_home'
  | 'camera_scanner'
  | 'camera_permission_denied'
  | 'no_camera_found'
  | 'camera_not_supported'
  | 'qr_scanner_not_initialized'
  | 'failed_to_read_file'
  | 'try_upload_instead'
  // Error Messages
  | 'something_went_wrong'
  | 'try_again'
  | 'admin_dashboard_error'
  | 'no_daily_guide_available'
  | 'daily_guide_check_back'
  // Admin Components
  | 'astroconnect_admin'
  | 'total_users'
  | 'active_users'
  | 'total_horoscopes'
  | 'qr_scans'
  | 'active_today'
  | 'total_scans'
  | 'daily_guides_generated'
  | 'recent_activity'
  | 'no_recent_activity'
  | 'admin_login'
  | 'access_admin_dashboard'
  | 'username'
  | 'password'
  | 'login'
  | 'invalid_credentials'
  | 'email_address'
  | 'enter_your_email'
  | 'enter_your_password'
  | 'signing_in'
  | 'sign_in'
  | 'demo_credentials'
  | 'secure_admin_access'
  | 'login_failed'
  | 'network_error_try_again'
  // Loading States
  | 'loading_ellipsis'
  // System Settings
  | 'default_language'
  | 'system_settings'
  | 'default_language_description'
  | 'save_changes'
  | 'changes_saved_successfully'
  | 'failed_to_save_changes'
  | 'loading'
  | 'saving';

export const UI_TRANSLATIONS: Record<UITextKey, { en: string; si: string }> = {
  // Navigation and Tabs
  horoscope: {
    en: 'Horoscope',
    si: 'ජ්‍යොතිෂය'
  },
  daily_guide: {
    en: 'Daily Guide',
    si: 'දෛනික මාර්ගෝපදේශය'
  },
  today_cosmic_guide: {
    en: "Today's Cosmic Guide",
    si: 'අද දිනේ කොස්මික් මාර්ගෝපදේශය'
  },
  general_reading: {
    en: 'General Reading',
    si: 'සාමාන්‍ය කියවීම'
  },
  love_relationships: {
    en: 'Love & Relationships',
    si: 'ආදරය සහ සබඳතා'
  },
  career_money: {
    en: 'Career & Money',
    si: 'වෘත්තිය සහ මුදල්'
  },
  health_wellness: {
    en: 'Health & Wellness',
    si: 'සෞඛ්‍යය සහ යහපැවැත්ම'
  },
  todays_lucky_elements: {
    en: "Today's Lucky Elements",
    si: 'අද දිනේ වාසනාවන්ත අංග'
  },
  lucky_number: {
    en: 'Lucky Number',
    si: 'වාසනාවන්ත අංකය'
  },
  lucky_color: {
    en: 'Lucky Color',
    si: 'වාසනාවන්ත වර්ණය'
  },
  lucky_time: {
    en: 'Lucky Time',
    si: 'වාසනාවන්ත වේලාව'
  },
  lucky_gem: {
    en: 'Lucky Gem',
    si: 'වාසනාවන්ත මැණික්'
  },
  daily_advice: {
    en: 'Daily Advice',
    si: 'දෛනික උපදෙස්'
  },
  compatible_signs_today: {
    en: 'Compatible Signs Today',
    si: 'අද දිනේ ගැළපෙන රාශි'
  },
  welcome: {
    en: 'Welcome',
    si: 'ආයුබෝවන්'
  },
  loading_cosmic_insights: {
    en: 'Loading your cosmic insights...',
    si: 'ඔබේ කොස්මික් අවබෝධය පූරණය වෙමින්...'
  },
  error_loading_dashboard: {
    en: 'Error Loading Dashboard',
    si: 'ඩෑෂ්බෝඩ් පූරණයේ දෝෂයක්'
  },
  configuration_error: {
    en: 'Configuration Error',
    si: 'වින්‍යාස දෝෂයක්'
  },
  session_expire_warning: {
    en: 'Your session will expire in',
    si: 'ඔබේ සැසිය අවසන් වනු ඇත්තේ'
  },
  settings: {
    en: 'Settings',
    si: 'සැකසුම්'
  },
  filter: {
    en: 'Filter',
    si: 'පෙරහන'
  },
  add_user: {
    en: 'Add User',
    si: 'පරිශීලකයා එක් කරන්න'
  },
  logout: {
    en: 'Logout',
    si: 'ඉවත් වන්න'
  },
  admin_user: {
    en: 'Admin User',
    si: 'පරිපාලක පරිශීලකයා'
  },
  manage_users_content_analytics: {
    en: 'Manage users, content, and analytics',
    si: 'පරිශීලකයින්, අන්තර්ගතය සහ විශ්ලේෂණ කළමනාකරණය කරන්න'
  },
  overview: {
    en: 'Overview',
    si: 'දළ විශ්ලේෂණය'
  },
  users: {
    en: 'Users',
    si: 'පරිශීලකයින්'
  },
  content: {
    en: 'Content',
    si: 'අන්තර්ගතය'
  },
  personal_horoscopes: {
    en: 'Personal Horoscopes',
    si: 'පුද්ගලික ජ්‍යොතිෂය'
  },
  analytics: {
    en: 'Analytics',
    si: 'විශ්ලේෂණ'
  },
  search_users: {
    en: 'Search users...',
    si: 'පරිශීලකයින් සොයන්න...'
  },
  name: {
    en: 'Name',
    si: 'නම'
  },
  email: {
    en: 'Email',
    si: 'ඊමේල්'
  },
  phone: {
    en: 'Phone',
    si: 'දුරකථනය'
  },
  zodiac: {
    en: 'Zodiac',
    si: 'රාශිය'
  },
  language: {
    en: 'Language',
    si: 'භාෂාව'
  },
  scans: {
    en: 'Scans',
    si: 'ස්කෑන්'
  },
  last_active: {
    en: 'Last Active',
    si: 'අවසන් ක්‍රියාකාරිත්වය'
  },
  actions: {
    en: 'Actions',
    si: 'ක්‍රියාමාර්ග'
  },
  never: {
    en: 'Never',
    si: 'කිසි විටෙකත්'
  },
  showing_users: {
    en: 'Showing',
    si: 'පෙන්වමින්'
  },
  previous: {
    en: 'Previous',
    si: 'පෙර'
  },
  next: {
    en: 'Next',
    si: 'ඊළඟ'
  },
  daily_guides: {
    en: 'Daily Guides',
    si: 'දෛනික මාර්ගෝපදේශ'
  },
  horoscopes: {
    en: 'Horoscopes',
    si: 'ජ්‍යොතිෂ'
  },
  all_signs: {
    en: 'All Signs',
    si: 'සියලුම රාශි'
  },
  all_types: {
    en: 'All Types',
    si: 'සියලුම වර්ග'
  },
  all_languages: {
    en: 'All Languages',
    si: 'සියලුම භාෂා'
  },
  english: {
    en: 'English',
    si: 'ඉංග්‍රීසි'
  },
  sinhala: {
    en: 'Sinhala',
    si: 'සිංහල'
  },
  daily: {
    en: 'Daily',
    si: 'දෛනික'
  },
  weekly: {
    en: 'Weekly',
    si: 'සතිපතා'
  },
  monthly: {
    en: 'Monthly',
    si: 'මාසික'
  },
  intense: {
    en: 'Intense',
    si: 'තීව්‍ර'
  },
  research_investigation: {
    en: 'Research and investigation skills are highlighted. Dig deeper into projects to uncover hidden opportunities.',
    si: 'පර්යේෂණ සහ විමර්ශන කුසලතා ඉස්මතු වේ. සැඟවුණු අවස්ථා සොයා ගැනීම සඳහා ව්‍යාපෘති ගැඹුරින් විමර්ශනය කරන්න.'
  },
  pay_attention_body: {
    en: 'Pay attention to your body\'s signals. Detoxification and cleansing activities are particularly beneficial today.',
    si: 'ඔබේ ශරීරයේ සංඥා වලට අවධානය යොමු කරන්න. විෂ ඉවත් කිරීම සහ පිරිසිදු කිරීමේ ක්‍රියාකාරකම් අද විශේෂයෙන් ප්‍රයෝජනවත් වේ.'
  },
  trust_intuition: {
    en: 'Trust your intuition in making important decisions. Your inner wisdom is particularly strong today.',
    si: 'වැදගත් තීරණ ගැනීමේදී ඔබේ අභ්‍යන්තර ප්‍රඥාව විශ්වාස කරන්න. ඔබේ අභ්‍යන්තර ප්‍රඥාව අද විශේෂයෙන් ශක්තිමත් වේ.'
  },
  deep_insights: {
    en: 'Deep insights and intuitive understanding guide you today. Trust your inner wisdom in all decisions.',
    si: 'ගැඹුරු අවබෝධය සහ අභ්‍යන්තර අවබෝධය අද ඔබට මග පෙන්වයි. සියලු තීරණවලදී ඔබේ අභ්‍යන්තර ප්‍රඥාව විශ්වාස කරන්න.'
  },
  intense_emotional_connections: {
    en: 'Intense emotional connections are possible today. Existing relationships deepen, and new ones may have profound significance.',
    si: 'අද තීව්‍ර චිත්තවේගීය සම්බන්ධතා ඇති විය හැක. පවතින සබඳතා ගැඹුරු වන අතර නව ඒවා ගැඹුරු වැදගත්කමක් ගත හැක.'
  },
  dig_deeper_projects: {
    en: 'Research and investigation skills are highlighted. Dig deeper into projects to uncover hidden opportunities.',
    si: 'පර්යේෂණ සහ විමර්ශන කුසලතා ඉස්මතු වේ. සැඟවුණු අවස්ථා සොයා ගැනීම සඳහා ව්‍යාපෘති ගැඹුරින් විමර්ශනය කරන්න.'
  },
  detoxification_cleansing: {
    en: 'Pay attention to your body\'s signals. Detoxification and cleansing activities are particularly beneficial today.',
    si: 'ඔබේ ශරීරයේ සංඥා වලට අවධානය යොමු කරන්න. විෂ ඉවත් කිරීම සහ පිරිසිදු කිරීමේ ක්‍රියාකාරකම් අද විශේෂයෙන් ප්‍රයෝජනවත් වේ.'
  },

  // Dashboard Messages
  no_personal_horoscope_available: {
    en: 'No Personal Horoscope Available',
    si: 'පුද්ගලික ජ්‍යොතිෂයක් නොමැත'
  },
  personalized_horoscope_content_message: {
    en: 'Your personalized horoscope content will appear here once added by the admin.',
    si: 'පරිපාලකයා විසින් එකතු කළ පසු ඔබේ පුද්ගලික ජ්‍යොතිෂ අන්තර්ගතය මෙහි දිස්වනු ඇත.'
  },

  // Landing Page
  your_personal_horoscope_daily_guide: {
    en: 'Your Personal Horoscope & Daily Guide',
    si: 'ඔබේ පුද්ගලික ජ්‍යොතිෂය සහ දෛනික මාර්ගෝපදේශය'
  },
  discover_cosmic_destiny: {
    en: 'Discover your cosmic destiny with personalized astrology insights',
    si: 'පුද්ගලික ජ්‍යොතිෂ අවබෝධයන් සමඟ ඔබේ කොස්මික් ඉරණම සොයා ගන්න'
  },
  daily_horoscopes: {
    en: 'Daily Horoscopes',
    si: 'දෛනික ජ්‍යොතිෂය'
  },
  daily_horoscopes_description: {
    en: 'Get personalized daily, weekly, and monthly predictions based on your zodiac sign',
    si: 'ඔබේ රාශි ලකුණ මත පදනම්ව පුද්ගලික දෛනික, සතිපතා සහ මාසික පුරෝකථන ලබා ගන්න'
  },
  lucky_guidance: {
    en: 'Lucky Guidance',
    si: 'වාසනාවන්ත මාර්ගෝපදේශය'
  },
  lucky_guidance_description: {
    en: 'Discover your lucky numbers, colors, and optimal times for important decisions',
    si: 'වැදගත් තීරණ සඳහා ඔබේ වාසනාවන්ත අංක, වර්ණ සහ ප්‍රශස්ත කාලයන් සොයා ගන්න'
  },
  qr_access: {
    en: 'QR Access',
    si: 'QR ප්‍රවේශය'
  },
  qr_access_description: {
    en: 'Instant access to your personalized dashboard with your unique QR code',
    si: 'ඔබේ අද්විතීය QR කේතය සමඟ ඔබේ පුද්ගලික ඩෑෂ්බෝඩ් වෙත ක්ෂණික ප්‍රවේශය'
  },
  scan_qr_code: {
    en: 'Scan QR Code',
    si: 'QR කේතය ස්කෑන් කරන්න'
  },
  upload_qr_image: {
    en: 'Upload QR Image',
    si: 'QR රූපය උඩුගත කරන්න'
  },
  scan_your_qr_code: {
    en: 'Scan Your QR Code',
    si: 'ඔබේ QR කේතය ස්කෑන් කරන්න'
  },
  qr_card_description: {
    en: 'Have a personalized QR card? Scan it to access your cosmic insights instantly',
    si: 'පුද්ගලික QR කාඩ්පතක් තිබේද? ඔබේ කොස්මික් අවබෝධයන් ක්ෂණිකව ප්‍රවේශ කිරීමට එය ස්කෑන් කරන්න'
  },
  qr_compatibility_tip: {
    en: '💡 Tip: Only AstroConnect QR codes will work here. Other QR codes (websites, WiFi, etc.) are not compatible.',
    si: '💡 ඉඟිය: මෙහි AstroConnect QR කේත පමණක් ක්‍රියා කරයි. අනෙකුත් QR කේත (වෙබ් අඩවි, WiFi, ආදිය) ගැළපෙන්නේ නැත.'
  },

  // QR Scanner
  use_camera: {
    en: 'Use Camera',
    si: 'කැමරාව භාවිතා කරන්න'
  },
  choose_file: {
    en: 'Choose File',
    si: 'ගොනුව තෝරන්න'
  },
  loading_scanner_library: {
    en: 'Loading scanner library...',
    si: 'ස්කෑනර් පුස්තකාලය පූරණය වෙමින්...'
  },
  starting_camera: {
    en: 'Starting camera...',
    si: 'කැමරාව ආරම්භ කරමින්...'
  },
  try_scanning_again: {
    en: 'Try Scanning Again',
    si: 'නැවත ස්කෑන් කරන්න'
  },
  dismiss: {
    en: 'Dismiss',
    si: 'ඉවත් කරන්න'
  },
  back_to_home: {
    en: 'Back to Home',
    si: 'මුල් පිටුවට'
  },
  camera_scanner: {
    en: 'Camera Scanner',
    si: 'කැමරා ස්කෑනරය'
  },
  camera_permission_denied: {
    en: 'Camera permission denied. Please allow camera access and try again.',
    si: 'කැමරා අවසරය ප්‍රතික්ෂේප කරන ලදී. කරුණාකර කැමරා ප්‍රවේශයට අවසර දී නැවත උත්සාහ කරන්න.'
  },
  no_camera_found: {
    en: 'No camera found on this device.',
    si: 'මෙම උපාංගයේ කැමරාවක් හමු නොවීය.'
  },
  camera_not_supported: {
    en: 'Camera not supported on this device.',
    si: 'මෙම උපාංගයේ කැමරාව සහාය නොදක්වයි.'
  },
  qr_scanner_not_initialized: {
    en: 'QR code scanner not properly initialized. Please refresh the page and try again.',
    si: 'QR කේත ස්කෑනරය නිසි ලෙස ආරම්භ කර නැත. කරුණාකර පිටුව නැවුම් කර නැවත උත්සාහ කරන්න.'
  },
  failed_to_read_file: {
    en: 'Failed to read the uploaded file. Please try again.',
    si: 'උඩුගත කළ ගොනුව කියවීමට අසමත් විය. කරුණාකර නැවත උත්සාහ කරන්න.'
  },
  try_upload_instead: {
    en: '💡 Try using "Upload Image" instead if camera access is blocked',
    si: '💡 කැමරා ප්‍රවේශය අවහිර කර ඇත්නම් ඒ වෙනුවට "රූපය උඩුගත කරන්න" භාවිතා කර බලන්න'
  },

  // Error Messages
  something_went_wrong: {
    en: 'Something went wrong',
    si: 'යමක් වැරදී ගියේය'
  },
  try_again: {
    en: 'Try Again',
    si: 'නැවත උත්සාහ කරන්න'
  },
  admin_dashboard_error: {
    en: 'Admin Dashboard Error',
    si: 'පරිපාලක ඩෑෂ්බෝඩ් දෝෂයක්'
  },
  no_daily_guide_available: {
    en: 'No Daily Guide Available',
    si: 'දෛනික මාර්ගෝපදේශයක් නොමැත'
  },
  daily_guide_check_back: {
    en: 'Your daily cosmic guide will appear here automatically. Please check back later.',
    si: 'ඔබේ දෛනික කොස්මික් මාර්ගෝපදේශය මෙහි ස්වයංක්‍රීයව දිස් වනු ඇත. කරුණාකර පසුව නැවත පරීක්ෂා කරන්න.'
  },

  // Admin Components
  astroconnect_admin: {
    en: 'AstroConnect Admin',
    si: 'AstroConnect පරිපාලක'
  },
  total_users: {
    en: 'Total Users',
    si: 'සම්පූර්ණ පරිශීලකයින්'
  },
  active_users: {
    en: 'Active Users',
    si: 'ක්‍රියාකාරී පරිශීලකයින්'
  },
  total_horoscopes: {
    en: 'Total Horoscopes',
    si: 'සම්පූර්ණ ජ්‍යොතිෂ'
  },
  qr_scans: {
    en: 'QR Scans',
    si: 'QR ස්කෑන්'
  },
  active_today: {
    en: 'Active Today',
    si: 'අද ක්‍රියාකාරී'
  },
  total_scans: {
    en: 'Total Scans',
    si: 'සම්පූර්ණ ස්කෑන්'
  },
  daily_guides_generated: {
    en: 'Daily Guides Generated',
    si: 'ජනනය කළ දෛනික මාර්ගෝපදේශ'
  },
  recent_activity: {
    en: 'Recent Activity',
    si: 'මෑත ක්‍රියාකාරකම්'
  },
  no_recent_activity: {
    en: 'No recent activity',
    si: 'මෑත ක්‍රියාකාරකම් නොමැත'
  },
  admin_login: {
    en: 'Admin Login',
    si: 'පරිපාලක පිවිසුම'
  },
  access_admin_dashboard: {
    en: 'Access the admin dashboard',
    si: 'පරිපාලක ඩෑෂ්බෝඩ් වෙත ප්‍රවේශ වන්න'
  },
  username: {
    en: 'Username',
    si: 'පරිශීලක නාමය'
  },
  password: {
    en: 'Password',
    si: 'මුරපදය'
  },
  login: {
    en: 'Login',
    si: 'පිවිසෙන්න'
  },
  invalid_credentials: {
    en: 'Invalid credentials',
    si: 'වලංගු නොවන අක්තපත්‍ර'
  },
  email_address: {
    en: 'Email Address',
    si: 'ඊමේල් ලිපිනය'
  },
  enter_your_email: {
    en: 'Enter your email',
    si: 'ඔබේ ඊමේල් ඇතුළත් කරන්න'
  },
  enter_your_password: {
    en: 'Enter your password',
    si: 'ඔබේ මුරපදය ඇතුළත් කරන්න'
  },
  signing_in: {
    en: 'Signing In...',
    si: 'පිවිසෙමින්...'
  },
  sign_in: {
    en: 'Sign In',
    si: 'පිවිසෙන්න'
  },
  demo_credentials: {
    en: 'Demo Credentials:',
    si: 'ආදර්ශන අක්තපත්‍ර:'
  },
  secure_admin_access: {
    en: 'Secure admin access to AstroConnect',
    si: 'AstroConnect වෙත ආරක්ෂිත පරිපාලක ප්‍රවේශය'
  },
  login_failed: {
    en: 'Login failed',
    si: 'පිවිසීම අසාර්ථක විය'
  },
  network_error_try_again: {
    en: 'Network error. Please try again.',
    si: 'ජාල දෝෂයක්. කරුණාකර නැවත උත්සාහ කරන්න.'
  },

  // Loading States
  loading_ellipsis: {
    en: 'Loading...',
    si: 'පූරණය වෙමින්...'
  },

  // System Settings
  default_language: {
    en: 'Default Language',
    si: 'පෙරනිමි භාෂාව'
  },
  system_settings: {
    en: 'System Settings',
    si: 'පද්ධති සැකසුම්'
  },
  default_language_description: {
    en: 'Set the default language for new users',
    si: 'නව පරිශීලකයින් සඳහා පෙරනිමි භාෂාව සකසන්න'
  },
  save_changes: {
    en: 'Save Changes',
    si: 'වෙනස්කම් සුරකින්න'
  },
  changes_saved_successfully: {
    en: 'Changes saved successfully',
    si: 'වෙනස්කම් සාර්ථකව සුරකින ලදී'
  },
  failed_to_save_changes: {
    en: 'Failed to save changes',
    si: 'වෙනස්කම් සුරැකීමට අසමත් විය'
  },
  loading: {
    en: 'Loading',
    si: 'පූරණය වෙමින්'
  },
  saving: {
    en: 'Saving',
    si: 'සුරකිමින්'
  }
};

/**
 * Get UI text translation for a given key and language
 */
export function getUIText(key: UITextKey, language: 'en' | 'si' = 'en'): string {
  const translation = UI_TRANSLATIONS[key];
  if (!translation) {
    console.warn(`UI translation not found for key: ${key}`);
    return key; // Return the key as fallback
  }
  return translation[language] || translation.en; // Fallback to English if translation missing
}

/**
 * Hook for using UI translations with current language context
 */
export function useUITranslation() {
  const { language } = useLanguage();

  const t = (key: UITextKey): string => {
    return getUIText(key, language);
  };

  return { t };
}
