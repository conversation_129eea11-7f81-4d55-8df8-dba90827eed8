import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { dailyReadingsScheduler } from '@/lib/scheduler';
import { ApiResponse, DashboardData, User, Horoscope, DailyGuide, LanguageCode } from '@/types';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');
  const language = searchParams.get('language') as 'en' | 'si' || 'en';

  if (!userId) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'User ID is required'
    }, { status: 400 });
  }

  try {
    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Use Sri Lankan time for consistency
    const now = new Date();
    const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
    const sriLankanTime = new Date(sriLankanTimeString);
    const todayDateString = sriLankanTime.getFullYear() + '-' +
                           String(sriLankanTime.getMonth() + 1).padStart(2, '0') + '-' +
                           String(sriLankanTime.getDate()).padStart(2, '0');
    const today = new Date(todayDateString);

    const zodiacSign = user.zodiacSign;
    const userLanguage = (language as LanguageCode) || user.languagePreference || 'en';

    // Get recent horoscopes for user's zodiac sign (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(today.getDate() - 7);

    const horoscopes = await prisma.horoscope.findMany({
      where: {
        zodiacSign: zodiacSign,
        date: {
          gte: sevenDaysAgo,
          lte: today
        },
        language: userLanguage
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Get personal horoscopes for this user (admin-created) - DEPRECATED
    const personalHoroscopes = await prisma.personalHoroscope.findMany({
      where: {
        userId: userId,
        isActive: true,
        language: userLanguage
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5 // Get latest 5 personal horoscopes
    });

    // Get birth chart for this user (calculated horoscope)
    const birthChart = await prisma.birthChart.findUnique({
      where: {
        userId: userId
      }
    });

    // Get daily zodiac reading for user's sign - get both languages
    const todayString = today.toISOString().split('T')[0];

    // Get both English and Sinhala versions for today
    const [dailyReadingEn, dailyReadingSi] = await Promise.all([
      prisma.dailyZodiacReading.findUnique({
        where: {
          zodiacSign_date_language: {
            zodiacSign: zodiacSign,
            date: new Date(todayString),
            language: 'en'
          }
        }
      }),
      prisma.dailyZodiacReading.findUnique({
        where: {
          zodiacSign_date_language: {
            zodiacSign: zodiacSign,
            date: new Date(todayString),
            language: 'si'
          }
        }
      })
    ]);

    // Use the user's preferred language as primary
    let dailyReading = userLanguage === 'si' ? dailyReadingSi : dailyReadingEn;

    // If no reading for today, trigger generation and get the most recent one
    if (!dailyReading) {
      console.log(`⚠️ No daily reading found for ${zodiacSign} on ${todayString}, checking scheduler...`);

      // Trigger scheduler to check and generate missing readings
      // This is a non-blocking call to ensure readings are generated
      dailyReadingsScheduler.checkAndGenerateReadings().catch(error => {
        console.error('Error triggering reading generation:', error);
      });

      // Get the most recent reading within the last 7 days as fallback
      dailyReading = await prisma.dailyZodiacReading.findFirst({
        where: {
          zodiacSign: zodiacSign,
          date: {
            gte: sevenDaysAgo,
            lte: today
          },
          language: userLanguage
        },
        orderBy: {
          date: 'desc'
        }
      });
    }

    // Organize horoscopes by type (get the most recent of each type)
    const todayHoroscope = horoscopes?.find(h => h.type === 'daily') || null;
    const weeklyHoroscope = horoscopes?.find(h => h.type === 'weekly') || null;
    const monthlyHoroscope = horoscopes?.find(h => h.type === 'monthly') || null;

    const dashboardData = {
      user: user as User,
      todayHoroscope: todayHoroscope as Horoscope,
      weeklyHoroscope: weeklyHoroscope as Horoscope,
      monthlyHoroscope: monthlyHoroscope as Horoscope,
      personalHoroscopes: personalHoroscopes, // DEPRECATED - will be replaced by birthChart
      birthChart: birthChart, // New calculated horoscope system
      dailyReading: dailyReading,
      // Include both language versions for client-side language switching
      dailyReadingTranslations: {
        en: dailyReadingEn,
        si: dailyReadingSi
      }
    };

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: dashboardData,
      message: 'Dashboard data retrieved successfully'
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, language } = await request.json();

    if (!userId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    // Update user's language preference
    await prisma.user.update({
      where: { id: userId },
      data: { languagePreference: language as LanguageCode }
    });

    return NextResponse.json<ApiResponse<{ updated: boolean }>>({
      success: true,
      data: { updated: true },
      message: 'Language preference updated successfully'
    });

  } catch (error) {
    console.error('Language update error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
