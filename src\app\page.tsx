'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import QRScanner from '@/components/QRScanner';
import PWAInstaller from '@/components/PWAInstaller';
import { useAuth } from '@/hooks/useAuth';
import { extractTokenFromUrl } from '@/utils/qr';
import { <PERSON><PERSON><PERSON>, <PERSON>, Star, Zap } from 'lucide-react';

export default function Home() {
  const [showScanner, setShowScanner] = useState(false);
  const [scanError, setScanError] = useState<string | null>(null);
  const { login } = useAuth();
  const router = useRouter();

  const handleScanSuccess = async (decodedText: string) => {
    console.log('QR Code scanned:', decodedText);

    // Extract token from the scanned URL
    const token = extractTokenFromUrl(decodedText);

    if (!token) {
      setScanError('Invalid QR code format');
      return;
    }

    // Authenticate with the token
    const result = await login(token);

    if (result.success) {
      router.push('/dashboard');
    } else {
      setScanError(result.error || 'Authentication failed');
    }
  };

  const handleScanError = (error: string) => {
    setScanError(error);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50"></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40"></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60"></div>
      </div>

      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-4">
        {!showScanner ? (
          <div className="text-center max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-center mb-4">
                <Sparkles className="w-8 h-8 text-yellow-400 mr-2" />
                <h1 className="text-4xl md:text-6xl font-bold text-white">
                  AstroConnect
                </h1>
                <Sparkles className="w-8 h-8 text-yellow-400 ml-2" />
              </div>
              <p className="text-xl md:text-2xl text-gray-300 mb-2">
                Your Personal Horoscope & Daily Guide
              </p>
              <p className="text-gray-400">
                Discover your cosmic destiny with personalized astrology insights
              </p>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Moon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Daily Horoscopes</h3>
                <p className="text-gray-300 text-sm">
                  Get personalized daily, weekly, and monthly predictions based on your zodiac sign
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Star className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Lucky Guidance</h3>
                <p className="text-gray-300 text-sm">
                  Discover your lucky numbers, colors, and optimal times for important decisions
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Zap className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">QR Access</h3>
                <p className="text-gray-300 text-sm">
                  Instant access to your personalized dashboard with your unique QR code
                </p>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="space-y-4">
              <button
                onClick={() => setShowScanner(true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Scan Your QR Code
              </button>

              <p className="text-gray-400 text-sm">
                Have a personalized QR card? Scan it to access your cosmic insights instantly
              </p>
            </div>

            {scanError && (
              <div className="mt-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
                <p className="text-red-300">{scanError}</p>
                <button
                  onClick={() => setScanError(null)}
                  className="text-red-200 hover:text-white underline mt-2"
                >
                  Try Again
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 max-w-md w-full">
            <QRScanner
              onScanSuccess={handleScanSuccess}
              onScanError={handleScanError}
            />

            <button
              onClick={() => {
                setShowScanner(false);
                setScanError(null);
              }}
              className="mt-4 w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Back to Home
            </button>
          </div>
        )}
      </div>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
