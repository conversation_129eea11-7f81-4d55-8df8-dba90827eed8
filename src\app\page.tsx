'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import QRScanner from '@/components/QRScanner';
import PWAInstaller from '@/components/PWAInstaller';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useAuth } from '@/hooks/useAuth';
import { useLanguage } from '@/hooks/useLanguage';
import { extractTokenFromUrl, detectQRCodeType } from '@/utils/qr';
import { Sparkles, Moon, Star, Zap } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

export default function Home() {
  const [showScanner, setShowScanner] = useState(false);
  const [scanError, setScanError] = useState<string | null>(null);
  const { login } = useAuth();
  const { setLanguage } = useLanguage();
  const router = useRouter();
  const { t } = useUITranslation();

  const handleLanguageChange = (newLanguage: 'en' | 'si') => {
    setLanguage(newLanguage);
  };

  const handleScanSuccess = async (decodedText: string) => {
    console.log('QR Code scanned:', decodedText);

    // Detect QR code type for better error handling
    const qrType = detectQRCodeType(decodedText);
    console.log('QR Code type detected:', qrType);

    // Check if the scanned text looks like a URL
    const isUrl = decodedText.startsWith('http://') || decodedText.startsWith('https://');

    // Extract token from the scanned URL
    const token = extractTokenFromUrl(decodedText);
    console.log('Token extracted:', token ? 'Yes' : 'No', token ? `(${token.substring(0, 8)}...)` : '');

    if (!token) {
      console.log('No token extracted, showing QR type-specific error');

      let errorMessage = 'This QR code is not compatible with AstroConnect. Please scan your personalized AstroConnect QR code.';

      switch (qrType) {
        case 'website-url':
        case 'google-url':
        case 'facebook-url':
        case 'instagram-url':
        case 'twitter-url':
        case 'youtube-url':
          errorMessage = 'This QR code links to a website, but it\'s not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'wifi':
          errorMessage = 'This is a WiFi QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'contact':
          errorMessage = 'This is a contact/business card QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'email':
          errorMessage = 'This is an email QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'phone':
          errorMessage = 'This is a phone number QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'sms':
          errorMessage = 'This is an SMS QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'text':
          errorMessage = 'This QR code contains plain text, not an AstroConnect access code. Please scan your personalized AstroConnect QR code instead.';
          break;
        case 'invalid':
        case 'unknown':
        default:
          errorMessage = 'This QR code is not recognized or compatible with AstroConnect. Please scan your personalized AstroConnect QR code.';
          break;
      }

      setScanError(errorMessage);
      return;
    }

    try {
      // Show loading state
      setScanError('Authenticating...');

      // Authenticate with the token
      const result = await login(token);

      if (result.success) {
        setScanError(null);
        router.push('/dashboard');
      } else {
        // Handle specific authentication errors with better context
        console.log('Authentication failed:', result.error);
        console.log('Original QR content:', decodedText);
        console.log('QR type detected:', qrType);

        const errorMsg = result.error?.toLowerCase() || '';

        if (errorMsg.includes('not found') || errorMsg.includes('invalid')) {
          // This means the token was extracted but not found in database
          console.log('Token not found in database, determining error message based on QR type');

          if (qrType === 'astroconnect-url') {
            setScanError('This appears to be an AstroConnect QR code, but it\'s not recognized in our system. It may be expired or invalid. Please contact support for assistance.');
          } else {
            // Use our detailed error messages for non-AstroConnect QR codes
            let specificError = 'This QR code is not compatible with AstroConnect. Please scan your personalized AstroConnect QR code.';

            switch (qrType) {
              case 'website-url':
              case 'google-url':
              case 'facebook-url':
              case 'instagram-url':
              case 'twitter-url':
              case 'youtube-url':
                specificError = 'This QR code links to a website, but it\'s not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'wifi':
                specificError = 'This is a WiFi QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'contact':
                specificError = 'This is a contact/business card QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'email':
                specificError = 'This is an email QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'phone':
                specificError = 'This is a phone number QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'sms':
                specificError = 'This is an SMS QR code, not an AstroConnect QR code. Please scan your personalized AstroConnect QR code instead.';
                break;
              case 'text':
                specificError = 'This QR code contains plain text, not an AstroConnect access code. Please scan your personalized AstroConnect QR code instead.';
                break;
            }

            console.log('Setting specific error:', specificError);
            setScanError(specificError);
          }
        } else if (errorMsg.includes('expired')) {
          setScanError('This QR code has expired. Please contact support for a new QR code.');
        } else {
          setScanError(result.error || 'Authentication failed. Please try again or contact support.');
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      setScanError('Connection error. Please check your internet connection and try again.');
    }
  };

  const handleScanError = (error: string) => {
    setScanError(error);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Language Switcher - Top Right */}
      <div className="absolute top-4 right-4 md:top-6 md:right-6 z-50">
        <div className="bg-white/10 backdrop-blur-md rounded-full p-2 border border-white/20 shadow-lg">
          <LanguageSwitcher onLanguageChange={handleLanguageChange} />
        </div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50"></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40"></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60"></div>
      </div>

      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-4 pt-20 md:pt-4">
        {!showScanner ? (
          <div className="text-center max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-center mb-4">
                <Sparkles className="w-8 h-8 text-yellow-400 mr-2" />
                <h1 className="text-4xl md:text-6xl font-bold text-white">
                  AstroConnect
                </h1>
                <Sparkles className="w-8 h-8 text-yellow-400 ml-2" />
              </div>
              <p className="text-xl md:text-2xl text-gray-300 mb-2">
                {t('your_personal_horoscope_daily_guide')}
              </p>
              <p className="text-gray-400">
                {t('discover_cosmic_destiny')}
              </p>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Moon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('daily_horoscopes')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('daily_horoscopes_description')}
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Star className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('lucky_guidance')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('lucky_guidance_description')}
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Zap className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('qr_access')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('qr_access_description')}
                </p>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="space-y-4">
              <button
                onClick={() => setShowScanner(true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {t('scan_qr_code')}
              </button>

              <div className="text-center space-y-2">
                <p className="text-gray-400 text-sm">
                  {t('qr_card_description')}
                </p>
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 max-w-md mx-auto">
                  <p className="text-blue-300 text-xs">
                    {t('qr_compatibility_tip')}
                  </p>
                </div>
              </div>
            </div>

            {scanError && (
              <div className="mt-6 p-6 bg-red-500/20 border border-red-500/50 rounded-lg max-w-md mx-auto">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm font-bold">!</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-red-200 font-semibold mb-2">
                      {scanError === 'Authenticating...' ? 'Please Wait' : 'QR Code Issue'}
                    </h4>
                    <p className="text-red-300 text-sm leading-relaxed mb-4">{scanError}</p>
                    {scanError !== 'Authenticating...' && (
                      <div className="flex flex-col sm:flex-row gap-2">
                        <button
                          onClick={() => {
                            setScanError(null);
                            setShowScanner(true);
                          }}
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          {t('try_scanning_again')}
                        </button>
                        <button
                          onClick={() => setScanError(null)}
                          className="text-red-200 hover:text-white underline text-sm"
                        >
                          {t('dismiss')}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 max-w-md w-full">
            <QRScanner
              onScanSuccess={handleScanSuccess}
              onScanError={handleScanError}
            />

            <button
              onClick={() => {
                setShowScanner(false);
                setScanError(null);
              }}
              className="mt-4 w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              {t('back_to_home')}
            </button>
          </div>
        )}
      </div>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
