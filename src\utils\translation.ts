// Client-side translation utility - NO PRISMA CLIENT USAGE
// This file should only contain client-safe functions

export async function translateText(
  text: string,
  targetLanguage: 'en' | 'si',
  sourceLanguage: 'en' | 'si' = 'en'
): Promise<string> {
  // If source and target are the same, return original text
  if (sourceLanguage === targetLanguage) {
    return text;
  }

  try {
    // Call translation API (server-side)
    const translatedText = await callTranslationAPI(text, sourceLanguage, targetLanguage);
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    // Return original text if translation fails
    return text;
  }
}

async function callTranslationAPI(
  text: string,
  sourceLanguage: 'en' | 'si',
  targetLanguage: 'en' | 'si'
): Promise<string> {
  const response = await fetch('/api/translate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text,
      sourceLanguage,
      targetLanguage
    })
  });

  if (!response.ok) {
    throw new Error('Translation API request failed');
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.error || 'Translation failed');
  }

  return data.translatedText;
}

export const LANGUAGE_NAMES = {
  en: 'English',
  si: 'සිංහල'
} as const;
