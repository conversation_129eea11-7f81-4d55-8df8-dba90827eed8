import { prisma } from '@/lib/prisma';

export async function translateText(
  text: string, 
  targetLanguage: 'en' | 'si',
  sourceLanguage: 'en' | 'si' = 'en'
): Promise<string> {
  // If source and target are the same, return original text
  if (sourceLanguage === targetLanguage) {
    return text;
  }

  // Check cache first
  const cachedTranslation = await getCachedTranslation(text, sourceLanguage, targetLanguage);
  if (cachedTranslation) {
    return cachedTranslation;
  }

  try {
    // Call Gemini API for translation
    const translatedText = await callGeminiTranslation(text, sourceLanguage, targetLanguage);
    
    // Cache the translation
    await cacheTranslation(text, translatedText, sourceLanguage, targetLanguage);
    
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    // Return original text if translation fails
    return text;
  }
}

async function getCachedTranslation(
  originalText: string,
  sourceLanguage: 'en' | 'si',
  targetLanguage: 'en' | 'si'
): Promise<string | null> {
  try {
    const cached = await prisma.translationCache.findFirst({
      where: {
        originalText,
        sourceLanguage,
        targetLanguage
      }
    });

    return cached?.translatedText || null;
  } catch (error) {
    console.error('Error fetching cached translation:', error);
    return null;
  }
}

async function cacheTranslation(
  originalText: string,
  translatedText: string,
  sourceLanguage: 'en' | 'si',
  targetLanguage: 'en' | 'si'
): Promise<void> {
  try {
    await prisma.translationCache.create({
      data: {
        originalText,
        translatedText,
        sourceLanguage,
        targetLanguage
      }
    });
  } catch (error) {
    console.error('Error caching translation:', error);
  }
}

async function callGeminiTranslation(
  text: string,
  sourceLanguage: 'en' | 'si',
  targetLanguage: 'en' | 'si'
): Promise<string> {
  const response = await fetch('/api/translate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text,
      sourceLanguage,
      targetLanguage
    })
  });

  if (!response.ok) {
    throw new Error('Translation API request failed');
  }

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Translation failed');
  }

  return data.translatedText;
}

export const LANGUAGE_NAMES = {
  en: 'English',
  si: 'සිංහල'
} as const;
