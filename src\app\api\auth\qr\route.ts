import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { isValidQRToken } from '@/utils/qr';
import { isValidUUID, sanitizeString } from '@/utils/validation';
import { normalizeIP } from '@/utils/security';
import { ApiResponse, User } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // Get client IP for logging
    const clientIP = normalizeIP(request.headers.get('x-forwarded-for') || 'unknown');

    const body = await request.json();
    const { token } = body;

    // Input validation
    if (!token || typeof token !== 'string') {
      console.warn(`Invalid token format from IP: ${clientIP}`);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid QR token format'
      }, { status: 400 });
    }

    const sanitizedToken = sanitizeString(token);

    if (!isValidQRToken(sanitizedToken)) {
      console.warn(`Invalid QR token from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid QR token format'
      }, { status: 400 });
    }

    // Find user by QR token
    const mapping = await prisma.qrCodeMapping.findUnique({
      where: { qrToken: sanitizedToken },
      include: {
        user: true
      }
    });

    if (!mapping) {
      console.warn(`QR token not found from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'QR code not found or invalid'
      }, { status: 404 });
    }

    // Update scan count and last scanned time
    await prisma.qrCodeMapping.update({
      where: { qrToken: sanitizedToken },
      data: {
        scanCount: mapping.scanCount + 1,
        lastScanned: new Date()
      }
    });

    const user = mapping.user as User;

    // Add session information with 30-minute expiration
    const sessionExpiry = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
    const userWithSession = {
      ...user,
      sessionExpiry: sessionExpiry.toISOString(),
      sessionStarted: new Date().toISOString()
    };

    // Log successful authentication
    console.log(`Successful QR authentication for user: ${user.id} from IP: ${clientIP}`);

    return NextResponse.json<ApiResponse<typeof userWithSession>>({
      success: true,
      data: userWithSession,
      message: 'Authentication successful'
    });

  } catch (error) {
    console.error('QR authentication error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token = searchParams.get('token');

  if (!token || !isValidQRToken(token)) {
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Invalid or missing QR token'
    }, { status: 400 });
  }

  try {
    // Check if QR token exists
    const mapping = await prisma.qrCodeMapping.findUnique({
      where: { qrToken: token }
    });

    if (!mapping) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'QR code not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse<{ exists: boolean }>>({
      success: true,
      data: { exists: true },
      message: 'QR token is valid'
    });

  } catch (error) {
    console.error('QR validation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
