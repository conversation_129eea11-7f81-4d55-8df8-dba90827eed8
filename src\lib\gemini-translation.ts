import { GoogleGenerative<PERSON><PERSON> } from '@google/generative-ai';
import { ZodiacSign, LanguageCode } from '@/types';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

export interface DailyReadingTranslation {
  generalReading: string;
  loveReading: string;
  careerReading: string;
  healthReading: string;
  luckyColor: string;
  luckyTime: string;
  luckyGem: string;
  advice: string;
  mood: string;
  compatibility: string;
}

// Rate limiting for translation API calls
let lastTranslationCall = 0;
const TRANSLATION_INTERVAL = 3000; // 3 seconds between translation calls

export async function translateDailyReading(
  englishReading: any,
  zodiacSign: ZodiacSign,
  targetLanguage: LanguageCode = 'si'
): Promise<DailyReadingTranslation> {
  if (targetLanguage === 'en') {
    // Return original if target is English
    return {
      generalReading: englishReading.generalReading,
      loveReading: englishReading.loveReading,
      careerReading: englishReading.careerReading,
      healthReading: englishReading.healthReading,
      luckyColor: englishReading.luckyColor,
      luckyTime: englishReading.luckyTime,
      luckyGem: englishReading.luckyGem,
      advice: englishReading.advice,
      mood: englishReading.mood,
      compatibility: englishReading.compatibility
    };
  }

  try {
    // Rate limiting
    const now = Date.now();
    const timeSinceLastCall = now - lastTranslationCall;
    if (timeSinceLastCall < TRANSLATION_INTERVAL) {
      await new Promise(resolve => setTimeout(resolve, TRANSLATION_INTERVAL - timeSinceLastCall));
    }
    lastTranslationCall = Date.now();

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Get zodiac sign in Sinhala
    const zodiacSinhala = getZodiacSignInSinhala(zodiacSign);

    const prompt = `
You are an expert translator specializing in Sri Lankan astrology and spiritual content. 
Translate the following daily horoscope reading from English to Sinhala for ${zodiacSign.toUpperCase()} (${zodiacSinhala}).

CRITICAL TRANSLATION GUIDELINES:
1. Use proper traditional Sinhala astrological terminology
2. Maintain the spiritual and mystical tone appropriate for horoscope readings
3. Use respectful, formal Sinhala suitable for spiritual guidance
4. For zodiac signs, use traditional Sinhala names
5. For lucky elements (colors, gems, times), use culturally appropriate Sinhala terms
6. Ensure natural flow in Sinhala - avoid word-for-word translation
7. Preserve the meaning and emotional impact of the original text
8. Use native Sinhala expressions for astrological concepts

ZODIAC SIGN TRANSLATIONS:
- Aries = මේෂ (Mesha)
- Taurus = වෘෂභ (Vrushabha) 
- Gemini = මිථුන (Mithuna)
- Cancer = කටක (Kataka)
- Leo = සිංහ (Simha)
- Virgo = කන්‍යා (Kanya)
- Libra = තුලා (Thula)
- Scorpio = වෘශ්චික (Vrushchika)
- Sagittarius = ධනු (Dhanu)
- Capricorn = මකර (Makara)
- Aquarius = කුම්භ (Kumbha)
- Pisces = මීන (Meena)

ENGLISH CONTENT TO TRANSLATE:

**General Reading:**
${englishReading.generalReading}

**Love & Relationships:**
${englishReading.loveReading}

**Career & Money:**
${englishReading.careerReading}

**Health & Wellness:**
${englishReading.healthReading}

**Lucky Color:**
${englishReading.luckyColor}

**Lucky Time:**
${englishReading.luckyTime}

**Lucky Gem:**
${englishReading.luckyGem}

**Advice:**
${englishReading.advice}

**Mood:**
${englishReading.mood}

**Compatibility:**
${englishReading.compatibility}

Please provide the translation in the following JSON format (return ONLY the JSON, no additional text):

{
  "generalReading": "Sinhala translation here",
  "loveReading": "Sinhala translation here", 
  "careerReading": "Sinhala translation here",
  "healthReading": "Sinhala translation here",
  "luckyColor": "Sinhala translation here",
  "luckyTime": "Sinhala translation here", 
  "luckyGem": "Sinhala translation here",
  "advice": "Sinhala translation here",
  "mood": "Sinhala translation here",
  "compatibility": "Sinhala translation here"
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text().trim();

    // Clean up the response to extract JSON
    let jsonText = text;
    if (text.includes('```json')) {
      jsonText = text.split('```json')[1].split('```')[0].trim();
    } else if (text.includes('```')) {
      jsonText = text.split('```')[1].trim();
    }

    try {
      const translatedContent = JSON.parse(jsonText);
      return translatedContent;
    } catch (parseError) {
      console.error('Failed to parse translation JSON:', parseError);
      console.error('Raw response:', text);
      
      // Fallback: return original content if parsing fails
      return {
        generalReading: englishReading.generalReading,
        loveReading: englishReading.loveReading,
        careerReading: englishReading.careerReading,
        healthReading: englishReading.healthReading,
        luckyColor: englishReading.luckyColor,
        luckyTime: englishReading.luckyTime,
        luckyGem: englishReading.luckyGem,
        advice: englishReading.advice,
        mood: englishReading.mood,
        compatibility: englishReading.compatibility
      };
    }

  } catch (error) {
    console.error('Translation error:', error);
    
    // Return original content if translation fails
    return {
      generalReading: englishReading.generalReading,
      loveReading: englishReading.loveReading,
      careerReading: englishReading.careerReading,
      healthReading: englishReading.healthReading,
      luckyColor: englishReading.luckyColor,
      luckyTime: englishReading.luckyTime,
      luckyGem: englishReading.luckyGem,
      advice: englishReading.advice,
      mood: englishReading.mood,
      compatibility: englishReading.compatibility
    };
  }
}

function getZodiacSignInSinhala(zodiacSign: ZodiacSign): string {
  const zodiacMap: Record<ZodiacSign, string> = {
    aries: 'මේෂ',
    taurus: 'වෘෂභ',
    gemini: 'මිථුන',
    cancer: 'කටක',
    leo: 'සිංහ',
    virgo: 'කන්‍යා',
    libra: 'තුලා',
    scorpio: 'වෘශ්චික',
    sagittarius: 'ධනු',
    capricorn: 'මකර',
    aquarius: 'කුම්භ',
    pisces: 'මීන'
  };
  
  return zodiacMap[zodiacSign] || zodiacSign;
}

// Batch translate all readings for a specific date
export async function translateAllDailyReadings(
  englishReadings: any[],
  targetLanguage: LanguageCode = 'si'
): Promise<any[]> {
  const translatedReadings = [];
  
  for (const reading of englishReadings) {
    try {
      console.log(`🔄 Translating reading for ${reading.zodiacSign}...`);
      
      const translated = await translateDailyReading(
        reading,
        reading.zodiacSign,
        targetLanguage
      );
      
      translatedReadings.push({
        ...reading,
        ...translated,
        language: targetLanguage
      });
      
      // Small delay between translations
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`Error translating reading for ${reading.zodiacSign}:`, error);
      
      // Add original reading with target language if translation fails
      translatedReadings.push({
        ...reading,
        language: targetLanguage
      });
    }
  }
  
  return translatedReadings;
}
