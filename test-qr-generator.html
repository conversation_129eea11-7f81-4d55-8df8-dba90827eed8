<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator for Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        input, button {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            background: #4CAF50;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        #qrcode {
            text-align: center;
            margin: 20px 0;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .sample-tokens {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .token-button {
            background: #2196F3;
            margin: 5px;
            padding: 8px 12px;
            display: inline-block;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .token-button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 QR Code Generator for AstroConnect Testing</h1>
        
        <div class="sample-tokens">
            <h3>📋 Sample Test Tokens (Click to use):</h3>
            <div class="token-button" onclick="generateQR('http://localhost:3000/auth?token=user123')">User Token: user123</div>
            <div class="token-button" onclick="generateQR('http://localhost:3000/auth?token=astro456')">User Token: astro456</div>
            <div class="token-button" onclick="generateQR('http://localhost:3000/auth?token=cosmic789')">User Token: cosmic789</div>
            <div class="token-button" onclick="generateQR('Hello World')">Simple Text</div>
        </div>

        <h3>🔧 Custom QR Code:</h3>
        <input type="text" id="textInput" placeholder="Enter text or URL (e.g., http://localhost:3000/auth?token=yourtoken)" 
               value="http://localhost:3000/auth?token=test123">
        <button onclick="generateCustomQR()">Generate QR Code</button>
        
        <div id="qrcode"></div>
        
        <div style="margin-top: 20px; font-size: 14px; opacity: 0.8;">
            <p>📱 <strong>How to test:</strong></p>
            <ol>
                <li>Generate a QR code above</li>
                <li>Open <a href="http://localhost:3000" target="_blank" style="color: #87CEEB;">http://localhost:3000</a> on your phone</li>
                <li>Click "Scan Your QR Code"</li>
                <li>Point your phone camera at the QR code on this screen</li>
                <li>The app should scan and authenticate automatically</li>
            </ol>
        </div>
    </div>

    <script>
        function generateQR(text) {
            const qrCodeDiv = document.getElementById('qrcode');
            qrCodeDiv.innerHTML = '';
            
            QRCode.toCanvas(text, { width: 300, margin: 2 }, function (error, canvas) {
                if (error) {
                    console.error(error);
                    qrCodeDiv.innerHTML = '<p style="color: red;">Error generating QR code</p>';
                } else {
                    qrCodeDiv.appendChild(canvas);
                    qrCodeDiv.innerHTML += `<p style="color: #333; margin-top: 10px; font-size: 14px;">QR Code for: ${text}</p>`;
                }
            });
        }

        function generateCustomQR() {
            const text = document.getElementById('textInput').value;
            if (text.trim()) {
                generateQR(text);
            } else {
                alert('Please enter some text or URL');
            }
        }

        // Generate default QR code on page load
        window.onload = function() {
            generateQR('http://localhost:3000/auth?token=test123');
        };
    </script>
</body>
</html>
