<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .result {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        .token-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .token-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .token-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AstroConnect Authentication Flow Test</h1>
        
        <div class="test-section">
            <h3>📋 Available Test Tokens:</h3>
            <div class="token-list">
                <div class="token-item" onclick="testToken('user123')">
                    <strong>user123</strong><br>
                    <small>John Doe (Aries)</small>
                </div>
                <div class="token-item" onclick="testToken('astro456')">
                    <strong>astro456</strong><br>
                    <small>Jane Smith (Leo)</small>
                </div>
                <div class="token-item" onclick="testToken('cosmic789')">
                    <strong>cosmic789</strong><br>
                    <small>Kasun Perera (Scorpio)</small>
                </div>
                <div class="token-item" onclick="testToken('test123')">
                    <strong>test123</strong><br>
                    <small>Nimal Silva (Pisces)</small>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Tests:</h3>
            <button onclick="testAllTokens()">Test All Tokens</button>
            <button onclick="testInvalidToken()">Test Invalid Token</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📊 Dashboard Test:</h3>
            <p>After successful authentication, test the dashboard:</p>
            <button onclick="testDashboard('user123')">Test Dashboard (user123)</button>
            <button onclick="testDashboard('astro456')">Test Dashboard (astro456)</button>
            
            <div id="dashboardResults"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function addResult(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function addDashboardResult(message, isError = false) {
            const results = document.getElementById('dashboardResults');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testToken(token) {
            addResult(`Testing token: ${token}`);
            
            try {
                const response = await fetch(`${API_BASE}/auth/qr`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ SUCCESS: ${data.message}`);
                    addResult(`User: ${data.data.name} (${data.data.zodiacSign})`);
                    addResult(`Session expires: ${new Date(data.data.sessionExpiry).toLocaleString()}`);
                } else {
                    addResult(`❌ FAILED: ${data.error}`, true);
                }
            } catch (error) {
                addResult(`❌ ERROR: ${error.message}`, true);
            }
        }

        async function testAllTokens() {
            const tokens = ['user123', 'astro456', 'cosmic789', 'test123'];
            addResult('🚀 Testing all tokens...');
            
            for (const token of tokens) {
                await testToken(token);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            }
            
            addResult('✅ All token tests completed');
        }

        async function testInvalidToken() {
            addResult('Testing invalid token...');
            await testToken('invalid-token-123');
        }

        async function testDashboard(token) {
            addDashboardResult(`Testing dashboard access with token: ${token}`);
            
            try {
                // First authenticate
                const authResponse = await fetch(`${API_BASE}/auth/qr`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                const authData = await authResponse.json();
                
                if (!authData.success) {
                    addDashboardResult(`❌ Auth failed: ${authData.error}`, true);
                    return;
                }

                const userId = authData.data.id;
                addDashboardResult(`✅ Authenticated as: ${authData.data.name}`);

                // Test dashboard API
                const dashResponse = await fetch(`${API_BASE}/dashboard?userId=${userId}&language=en`);
                const dashData = await dashResponse.json();
                
                if (dashData.success) {
                    addDashboardResult(`✅ Dashboard loaded successfully`);
                    addDashboardResult(`User: ${dashData.data.user.name} (${dashData.data.user.zodiacSign})`);
                    
                    if (dashData.data.dailyReading) {
                        addDashboardResult(`✅ Daily reading available`);
                        addDashboardResult(`Lucky number: ${dashData.data.dailyReading.luckyNumber}`);
                        addDashboardResult(`Lucky color: ${dashData.data.dailyReading.luckyColor}`);
                        addDashboardResult(`Mood: ${dashData.data.dailyReading.mood}`);
                    } else {
                        addDashboardResult(`⚠️ No daily reading found`, true);
                    }
                    
                    if (dashData.data.personalHoroscopes && dashData.data.personalHoroscopes.length > 0) {
                        addDashboardResult(`✅ ${dashData.data.personalHoroscopes.length} personal horoscope(s) found`);
                    } else {
                        addDashboardResult(`⚠️ No personal horoscopes found`);
                    }
                } else {
                    addDashboardResult(`❌ Dashboard failed: ${dashData.error}`, true);
                }
            } catch (error) {
                addDashboardResult(`❌ ERROR: ${error.message}`, true);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('dashboardResults').innerHTML = '';
        }

        // Auto-test on load
        window.onload = function() {
            addResult('🎯 Authentication Flow Tester Ready');
            addResult('Click on a token above or use the test buttons');
        };
    </script>
</body>
</html>
