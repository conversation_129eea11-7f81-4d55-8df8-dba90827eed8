'use client';

import { useState, useEffect } from 'react';
import { User, UserWithSession } from '@/types';

export function useAuth() {
  const [user, setUser] = useState<UserWithSession | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if session is expired
  const isSessionExpired = (user: UserWithSession | null): boolean => {
    if (!user || !user.sessionExpiry) return true;
    return new Date() > new Date(user.sessionExpiry);
  };

  useEffect(() => {
    // Check for stored user data on component mount
    const storedUser = localStorage.getItem('astroconnect_user');
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser) as UserWithSession;

        // Check if session is expired
        if (isSessionExpired(parsedUser)) {
          console.log('Session expired, removing stored user data');
          localStorage.removeItem('astroconnect_user');
          setUser(null);
        } else {
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('astroconnect_user');
      }
    }
    setLoading(false);
  }, []);

  // Auto-logout when session expires
  useEffect(() => {
    if (!user || !user.sessionExpiry) return;

    const checkSessionExpiry = () => {
      if (isSessionExpired(user)) {
        console.log('Session expired, logging out user');
        logout();
      }
    };

    // Check every minute
    const interval = setInterval(checkSessionExpiry, 60000);

    // Also set a timeout for the exact expiry time
    const timeUntilExpiry = new Date(user.sessionExpiry).getTime() - Date.now();
    const timeout = setTimeout(() => {
      console.log('Session expired, logging out user');
      logout();
    }, timeUntilExpiry);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [user]);

  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await fetch('/api/auth/qr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (data.success && data.data) {
        const userWithSession = data.data as UserWithSession;

        // Verify session is not expired
        if (isSessionExpired(userWithSession)) {
          return { success: false, error: 'Session expired during authentication' };
        }

        setUser(userWithSession);
        localStorage.setItem('astroconnect_user', JSON.stringify(userWithSession));
        console.log(`User logged in with session expiring at: ${userWithSession.sessionExpiry}`);
        return { success: true };
      } else {
        return { success: false, error: data.error || 'Authentication failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const logout = () => {
    console.log('User logged out');
    setUser(null);
    localStorage.removeItem('astroconnect_user');
  };

  const updateUser = (updatedUser: UserWithSession) => {
    setUser(updatedUser);
    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));
  };

  const isAuthenticated = !!user && !isSessionExpired(user);

  // Get session time remaining in minutes
  const getSessionTimeRemaining = (): number => {
    if (!user || !user.sessionExpiry) return 0;
    const remaining = new Date(user.sessionExpiry).getTime() - Date.now();
    return Math.max(0, Math.floor(remaining / (1000 * 60))); // Convert to minutes
  };

  return {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateUser,
    getSessionTimeRemaining,
    isSessionExpired: () => isSessionExpired(user)
  };
}
