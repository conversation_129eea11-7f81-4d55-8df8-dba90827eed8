import { NextRequest, NextResponse } from 'next/server';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { dailyReadingsScheduler } from '@/lib/scheduler';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';

// Rate limiting for manual generation
let lastManualGeneration = 0;
const MANUAL_GENERATION_COOLDOWN = 60000; // 1 minute cooldown

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'status';

    if (action === 'status') {
      const status = dailyReadingsScheduler.getStatus();

      // Get today's readings count (using Sri Lankan date)
      const now = new Date();
      const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
      const sriLankanTime = new Date(sriLankanTimeString);
      const today = sriLankanTime.getFullYear() + '-' +
                   String(sriLankanTime.getMonth() + 1).padStart(2, '0') + '-' +
                   String(sriLankanTime.getDate()).padStart(2, '0');

      const todayReadings = await prisma.dailyZodiacReading.count({
        where: {
          date: new Date(today),
          language: 'en'
        }
      });

      // Get recent readings summary
      const recentReadings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        },
        select: {
          date: true,
          zodiacSign: true,
          createdAt: true
        },
        orderBy: { date: 'desc' }
      });

      // Group by date
      const readingsByDate = recentReadings.reduce((acc, reading) => {
        const dateKey = reading.date.toISOString().split('T')[0];
        if (!acc[dateKey]) {
          acc[dateKey] = { count: 0, signs: [] };
        }
        acc[dateKey].count++;
        acc[dateKey].signs.push(reading.zodiacSign);
        return acc;
      }, {} as Record<string, { count: number; signs: string[] }>);

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: {
          scheduler: status,
          todayReadingsCount: todayReadings,
          isComplete: todayReadings === 12,
          recentReadings: readingsByDate,
          serverTime: new Date().toISOString(),
          localTime: new Date().toLocaleString()
        },
        message: 'Scheduler status retrieved successfully'
      });
    }

    if (action === 'readings') {
      const date = searchParams.get('date') || new Date().toISOString().split('T')[0];

      const readings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(date),
          language: 'en'
        },
        orderBy: { zodiacSign: 'asc' }
      });

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { readings, date, count: readings.length },
        message: 'Daily readings retrieved successfully'
      });
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Invalid action parameter'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Error getting scheduler status:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to get scheduler status'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { action, date } = await request.json();

    if (action === 'generate') {
      // Check rate limiting
      const now = Date.now();
      const timeSinceLastGeneration = now - lastManualGeneration;

      if (timeSinceLastGeneration < MANUAL_GENERATION_COOLDOWN) {
        const remainingTime = Math.ceil((MANUAL_GENERATION_COOLDOWN - timeSinceLastGeneration) / 1000);
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: `Please wait ${remainingTime} seconds before generating again to avoid API rate limits`
        }, { status: 429 });
      }

      lastManualGeneration = now;
      console.log('🔄 Admin triggered manual reading generation');

      // Use Sri Lankan date if no specific date provided
      let targetDate = date;
      if (!targetDate) {
        const now = new Date();
        const sriLankanTimeString = now.toLocaleString("en-US", {timeZone: "Asia/Colombo"});
        const sriLankanTime = new Date(sriLankanTimeString);
        targetDate = sriLankanTime.getFullYear() + '-' +
                     String(sriLankanTime.getMonth() + 1).padStart(2, '0') + '-' +
                     String(sriLankanTime.getDate()).padStart(2, '0');
      }

      const readings = await dailyReadingsScheduler.generateNow(targetDate);

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: {
          readings,
          count: readings.length,
          date: targetDate
        },
        message: `Successfully generated ${readings.length} daily readings for ${targetDate}`
      });
    }

    if (action === 'force-check') {
      console.log('🔄 Admin triggered force check for current Sri Lankan date');

      await dailyReadingsScheduler.forceCheckNow();

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { message: 'Force check completed' },
        message: 'Successfully triggered force check for current Sri Lankan date'
      });
    }

    if (action === 'status') {
      const status = dailyReadingsScheduler.getStatus();

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { scheduler: status },
        message: 'Scheduler status retrieved successfully'
      });
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Invalid action. Use "generate", "force-check", or "status"'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Error in scheduler action:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to execute scheduler action'
    }, { status: 500 });
  }
}
