import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';

export interface AdminTokenPayload {
  adminId: string;
  email: string;
  name: string;
  role: string;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token for admin
export function generateAdminToken(payload: AdminTokenPayload): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '24h',
    issuer: 'astroconnect-admin'
  });
}

// Verify JWT token
export function verifyAdminToken(token: string): AdminTokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'astroconnect-admin'
    }) as AdminTokenPayload;
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// Extract admin token from request
export function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {
  try {
    // Check Authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      return verifyAdminToken(token);
    }

    // Check cookie
    const tokenCookie = request.cookies.get('admin-token');
    if (tokenCookie) {
      return verifyAdminToken(tokenCookie.value);
    }

    return null;
  } catch (error) {
    console.error('Error extracting admin from request:', error);
    return null;
  }
}

// Validate admin permissions
export function requireAdminAuth(admin: AdminTokenPayload | null): boolean {
  return admin !== null && admin.role === 'admin';
}

// Generate secure random password for demo
export function generateSecurePassword(length: number = 12): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}
