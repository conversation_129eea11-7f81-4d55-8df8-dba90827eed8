import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateAllDailyReadings } from '@/lib/gemini';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { date } = await request.json();
    
    if (!date) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Date is required'
      }, { status: 400 });
    }

    console.log('🌟 Generating daily zodiac readings for:', date);

    // Check if readings already exist for this date
    const existingReadings = await prisma.dailyZodiacReading.findMany({
      where: {
        date: new Date(date),
        language: 'en'
      }
    });

    if (existingReadings.length === 12) {
      console.log('✅ Readings already exist for', date);
      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { readings: existingReadings },
        message: 'Daily readings already exist for this date'
      });
    }

    // Generate new readings using Gemini API
    console.log('🤖 Generating new readings with Gemini API...');
    const newReadings = await generateAllDailyReadings(date);

    // Save readings to database
    const savedReadings = [];
    for (const reading of newReadings) {
      try {
        const saved = await prisma.dailyZodiacReading.upsert({
          where: {
            zodiacSign_date_language: {
              zodiacSign: reading.zodiacSign,
              date: new Date(date),
              language: 'en'
            }
          },
          update: {
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
            updatedAt: new Date()
          },
          create: {
            zodiacSign: reading.zodiacSign,
            date: new Date(date),
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
            language: 'en'
          }
        });
        savedReadings.push(saved);
        console.log(`✅ Saved reading for ${reading.zodiacSign}`);
      } catch (error) {
        console.error(`❌ Error saving reading for ${reading.zodiacSign}:`, error);
      }
    }

    console.log(`🎉 Successfully generated and saved ${savedReadings.length} daily readings`);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: { readings: savedReadings },
      message: `Generated ${savedReadings.length} daily zodiac readings`
    });

  } catch (error) {
    console.error('❌ Error generating daily readings:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to generate daily readings'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const zodiacSign = searchParams.get('zodiacSign');

    console.log('📖 Fetching daily readings for:', { date, zodiacSign });

    if (zodiacSign) {
      // Get reading for specific zodiac sign
      const reading = await prisma.dailyZodiacReading.findUnique({
        where: {
          zodiacSign_date_language: {
            zodiacSign: zodiacSign as any,
            date: new Date(date),
            language: 'en'
          }
        }
      });

      if (!reading) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'No reading found for this zodiac sign and date'
        }, { status: 404 });
      }

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { reading },
        message: 'Daily reading retrieved successfully'
      });
    } else {
      // Get all readings for the date
      const readings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(date),
          language: 'en'
        },
        orderBy: {
          zodiacSign: 'asc'
        }
      });

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { readings },
        message: 'Daily readings retrieved successfully'
      });
    }

  } catch (error) {
    console.error('❌ Error fetching daily readings:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to fetch daily readings'
    }, { status: 500 });
  }
}
