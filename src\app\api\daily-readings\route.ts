import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateAllDailyReadings } from '@/lib/gemini';
import { translateAllDailyReadings } from '@/lib/gemini-translation';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { date } = await request.json();
    
    if (!date) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Date is required'
      }, { status: 400 });
    }

    console.log('🌟 Generating daily zodiac readings for:', date);

    // Check if readings already exist for this date (both English and Sinhala)
    const existingEnglishReadings = await prisma.dailyZodiacReading.findMany({
      where: {
        date: new Date(date),
        language: 'en'
      }
    });

    const existingSinhalaReadings = await prisma.dailyZodiacReading.findMany({
      where: {
        date: new Date(date),
        language: 'si'
      }
    });

    if (existingEnglishReadings.length === 12 && existingSinhalaReadings.length === 12) {
      console.log('✅ Readings already exist for both languages on', date);
      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: {
          englishReadings: existingEnglishReadings,
          sinhalaReadings: existingSinhalaReadings,
          total: existingEnglishReadings.length + existingSinhalaReadings.length
        },
        message: 'Daily readings already exist for this date in both languages'
      });
    }

    // Generate English readings if they don't exist
    let englishReadings = existingEnglishReadings;
    if (existingEnglishReadings.length < 12) {
      console.log('🤖 Generating new English daily readings with Gemini API...');
      const newEnglishReadings = await generateAllDailyReadings(date);

      // Save English readings to database
      const savedEnglishReadings = [];
        try {
          const saved = await prisma.dailyZodiacReading.upsert({
            where: {
              zodiacSign_date_language: {
                zodiacSign: reading.zodiacSign,
                date: new Date(date),
                language: 'en'
              }
            },
            update: {
              generalReading: reading.generalReading,
              loveReading: reading.loveReading,
              careerReading: reading.careerReading,
              healthReading: reading.healthReading,
              luckyNumber: reading.luckyNumber,
              luckyColor: reading.luckyColor,
              luckyTime: reading.luckyTime,
              luckyGem: reading.luckyGem,
              advice: reading.advice,
              mood: reading.mood,
              compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
              updatedAt: new Date()
            },
            create: {
              zodiacSign: reading.zodiacSign,
              date: new Date(date),
              generalReading: reading.generalReading,
              loveReading: reading.loveReading,
              careerReading: reading.careerReading,
              healthReading: reading.healthReading,
              luckyNumber: reading.luckyNumber,
              luckyColor: reading.luckyColor,
              luckyTime: reading.luckyTime,
              luckyGem: reading.luckyGem,
              advice: reading.advice,
              mood: reading.mood,
              compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
              language: 'en'
            }
          });
          savedEnglishReadings.push(saved);
          console.log(`✅ Saved English reading for ${reading.zodiacSign}`);
        } catch (error) {
          console.error(`❌ Error saving English reading for ${reading.zodiacSign}:`, error);
        }
      }

      englishReadings = savedEnglishReadings;
      console.log(`🎉 Successfully generated and saved ${savedEnglishReadings.length} English daily readings`);
    }

    // Generate Sinhala translations if they don't exist
    let sinhalaReadings = existingSinhalaReadings;
    if (existingSinhalaReadings.length < 12 && englishReadings.length > 0) {
      console.log('🔄 Generating Sinhala translations...');

      try {
        const translatedReadings = await translateAllDailyReadings(englishReadings, 'si');

        // Save Sinhala readings to database
        const savedSinhalaReadings = [];
        for (const reading of translatedReadings) {
          try {
            const saved = await prisma.dailyZodiacReading.upsert({
              where: {
                zodiacSign_date_language: {
                  zodiacSign: reading.zodiacSign,
                  date: new Date(date),
                  language: 'si'
                }
              },
              update: {
                generalReading: reading.generalReading,
                loveReading: reading.loveReading,
                careerReading: reading.careerReading,
                healthReading: reading.healthReading,
                luckyNumber: reading.luckyNumber,
                luckyColor: reading.luckyColor,
                luckyTime: reading.luckyTime,
                luckyGem: reading.luckyGem,
                advice: reading.advice,
                mood: reading.mood,
                compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                updatedAt: new Date()
              },
              create: {
                zodiacSign: reading.zodiacSign,
                date: new Date(date),
                generalReading: reading.generalReading,
                loveReading: reading.loveReading,
                careerReading: reading.careerReading,
                healthReading: reading.healthReading,
                luckyNumber: reading.luckyNumber,
                luckyColor: reading.luckyColor,
                luckyTime: reading.luckyTime,
                luckyGem: reading.luckyGem,
                advice: reading.advice,
                mood: reading.mood,
                compatibility: Array.isArray(reading.compatibility) ? reading.compatibility.join(', ') : reading.compatibility,
                language: 'si'
              }
            });
            savedSinhalaReadings.push(saved);
            console.log(`✅ Saved Sinhala reading for ${reading.zodiacSign}`);
          } catch (error) {
            console.error(`❌ Error saving Sinhala reading for ${reading.zodiacSign}:`, error);
          }
        }

        sinhalaReadings = savedSinhalaReadings;
        console.log(`🎉 Successfully generated and saved ${savedSinhalaReadings.length} Sinhala daily readings`);

      } catch (error) {
        console.error('❌ Error generating Sinhala translations:', error);
      }
    }

    const totalReadings = englishReadings.length + sinhalaReadings.length;
    console.log(`🎉 Total readings available: ${totalReadings} (${englishReadings.length} English + ${sinhalaReadings.length} Sinhala)`);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: {
        englishReadings,
        sinhalaReadings,
        total: totalReadings
      },
      message: `Generated daily zodiac readings: ${englishReadings.length} English + ${sinhalaReadings.length} Sinhala`
    });

  } catch (error) {
    console.error('❌ Error generating daily readings:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to generate daily readings'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const zodiacSign = searchParams.get('zodiacSign');

    console.log('📖 Fetching daily readings for:', { date, zodiacSign });

    if (zodiacSign) {
      // Get reading for specific zodiac sign
      const reading = await prisma.dailyZodiacReading.findUnique({
        where: {
          zodiacSign_date_language: {
            zodiacSign: zodiacSign as any,
            date: new Date(date),
            language: 'en'
          }
        }
      });

      if (!reading) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'No reading found for this zodiac sign and date'
        }, { status: 404 });
      }

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { reading },
        message: 'Daily reading retrieved successfully'
      });
    } else {
      // Get all readings for the date
      const readings = await prisma.dailyZodiacReading.findMany({
        where: {
          date: new Date(date),
          language: 'en'
        },
        orderBy: {
          zodiacSign: 'asc'
        }
      });

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { readings },
        message: 'Daily readings retrieved successfully'
      });
    }

  } catch (error) {
    console.error('❌ Error fetching daily readings:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to fetch daily readings'
    }, { status: 500 });
  }
}
