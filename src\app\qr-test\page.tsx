'use client';

import { useState } from 'react';
import QRCode from 'qrcode';

export default function QRTestPage() {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [testUrl, setTestUrl] = useState<string>('https://www.google.com');
  const [testType, setTestType] = useState<string>('website');

  const getTestContent = () => {
    switch (testType) {
      case 'website':
        return testUrl;
      case 'text':
        return 'Hello, this is just plain text!';
      case 'wifi':
        return 'WIFI:T:WPA;S:MyNetwork;P:MyPassword;;';
      case 'email':
        return 'mailto:<EMAIL>';
      case 'phone':
        return 'tel:+1234567890';
      case 'sms':
        return 'sms:+1234567890:Hello there!';
      default:
        return testUrl;
    }
  };

  const generateTestQR = async () => {
    try {
      const content = getTestContent();
      const qrDataUrl = await QRCode.toDataURL(content, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeUrl(qrDataUrl);
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h1 className="text-2xl font-bold text-white mb-6">QR Code Error Testing</h1>
          
          <div className="space-y-6">
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                QR Code Type:
              </label>
              <select
                value={testType}
                onChange={(e) => setTestType(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/30 rounded-lg text-white"
              >
                <option value="website">Website URL</option>
                <option value="text">Plain Text</option>
                <option value="wifi">WiFi Credentials</option>
                <option value="email">Email Address</option>
                <option value="phone">Phone Number</option>
                <option value="sms">SMS Message</option>
              </select>
            </div>

            {testType === 'website' && (
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Website URL:
                </label>
                <input
                  type="text"
                  value={testUrl}
                  onChange={(e) => setTestUrl(e.target.value)}
                  className="w-full px-3 py-2 bg-white/10 border border-white/30 rounded-lg text-white placeholder-gray-400"
                  placeholder="Enter any URL to test error handling"
                />
              </div>
            )}

            <div className="bg-gray-500/20 border border-gray-500/30 rounded-lg p-3">
              <p className="text-gray-300 text-sm">
                <strong>Content to encode:</strong> {getTestContent()}
              </p>
            </div>

            <button
              onClick={generateTestQR}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-all"
            >
              Generate Test QR Code
            </button>

            {qrCodeUrl && (
              <div className="text-center">
                <h3 className="text-white text-lg font-semibold mb-4">Test QR Code</h3>
                <div className="bg-white p-4 rounded-lg inline-block">
                  <img src={qrCodeUrl} alt="Test QR Code" className="w-64 h-64" />
                </div>
                <p className="text-gray-300 text-sm mt-4">
                  📱 Scan this QR code with the AstroConnect app to test the error handling
                </p>
                <p className="text-yellow-300 text-xs mt-2">
                  ⚠️ This QR code contains: {getTestContent()}
                </p>
              </div>
            )}

            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
              <h4 className="text-blue-300 font-semibold mb-2">How to Test:</h4>
              <ol className="text-blue-200 text-sm space-y-1 list-decimal list-inside">
                <li>Generate a test QR code above</li>
                <li>Go to the main AstroConnect page</li>
                <li>Click "Scan Your QR Code"</li>
                <li>Scan the test QR code you generated</li>
                <li>You should see a user-friendly error message</li>
              </ol>
            </div>

            <div className="text-center">
              <a
                href="/"
                className="inline-block bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-semibold py-2 px-4 rounded-lg transition-all"
              >
                Go to AstroConnect Main Page
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
