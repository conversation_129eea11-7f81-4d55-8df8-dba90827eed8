// Astrology Calculation Service
// Handahana (Birth Chart) calculations using Vedic Astrology principles

import { Origin, Horoscope } from 'circular-natal-horoscope-js';

export interface BirthDetails {
  birthDate: Date;
  birthTime: string; // "HH:MM" format
  birthPlace: string;
  latitude: number;
  longitude: number;
}

export interface PlanetPosition {
  name: string;
  longitude: number;
  latitude: number;
  sign: string;
  house: number;
  nakshatra: string;
  isRetrograde: boolean;
}

export interface HouseInfo {
  number: number;
  sign: string;
  cusp: number;
  lord: string;
}

export interface BirthChartData {
  // Basic Info
  ascendant: string;
  moonSign: string;
  sunSign: string;
  
  // Planetary Positions
  planets: PlanetPosition[];
  
  // Houses
  houses: HouseInfo[];
  
  // Aspects
  aspects: any[];
  
  // Nakshatras
  nakshatras: any[];
  
  // Dashas (Planetary Periods)
  dashas: any[];
}

// Vedic Zodiac Signs (Sidereal)
export const VEDIC_SIGNS = [
  'Aries', '<PERSON>rus', 'Gemini', '<PERSON>',
  '<PERSON>', 'Vir<PERSON>', '<PERSON><PERSON>', '<PERSON>or<PERSON>',
  'Sagittarius', 'Capricorn', 'Aquarius', '<PERSON>s<PERSON>'
];

// Nakshatras (27 Lunar Mansions)
export const NAKSHATRAS = [
  '<PERSON>wini', '<PERSON>hara<PERSON>', '<PERSON>rittika', '<PERSON>oh<PERSON>', 'Mr<PERSON>shira', 'Ardra',
  '<PERSON>una<PERSON>', '<PERSON>ya', '<PERSON>a', 'Ma<PERSON>a', '<PERSON>ur<PERSON>al<PERSON>i', '<PERSON>a <PERSON>al<PERSON>i',
  '<PERSON>ta', '<PERSON>tra', '<PERSON>wati', '<PERSON>ishakha', 'An<PERSON>', '<PERSON>ha',
  '<PERSON>la', '<PERSON>ur<PERSON> <PERSON>ha', 'Uttara Ashadha', 'Shravana', 'Dhanishta', 'Shatabhisha',
  'Purva Bhadrapada', 'Uttara Bhadrapada', 'Revati'
];

// Planet names in Vedic Astrology
export const VEDIC_PLANETS = {
  sun: 'Surya',
  moon: 'Chandra',
  mercury: 'Budha',
  venus: 'Shukra',
  mars: 'Mangal',
  jupiter: 'Guru',
  saturn: 'Shani',
  rahu: 'Rahu', // North Node
  ketu: 'Ketu'  // South Node
};

/**
 * Calculate birth chart using Vedic Astrology principles
 */
export async function calculateBirthChart(birthDetails: BirthDetails): Promise<BirthChartData> {
  try {
    console.log('🔮 Calculating birth chart for:', birthDetails);

    // Parse birth time
    const [hours, minutes] = birthDetails.birthTime.split(':').map(Number);
    
    // Create Origin object for calculations
    const origin = new Origin({
      year: birthDetails.birthDate.getFullYear(),
      month: birthDetails.birthDate.getMonth(), // 0-based month
      date: birthDetails.birthDate.getDate(),
      hour: hours,
      minute: minutes,
      latitude: birthDetails.latitude,
      longitude: birthDetails.longitude
    });

    // Create Horoscope with Sidereal (Vedic) zodiac
    const horoscope = new Horoscope({
      origin: origin,
      houseSystem: 'whole-sign', // Traditional Vedic house system
      zodiac: 'sidereal', // Vedic uses sidereal zodiac
      aspectPoints: ['bodies', 'points', 'angles'],
      aspectWithPoints: ['bodies', 'points', 'angles'],
      aspectTypes: ['major', 'minor'],
      customOrbs: {},
      language: 'en'
    });

    // Extract planetary positions
    const planets: PlanetPosition[] = [];
    
    // Process major planets
    const celestialBodies = horoscope.CelestialBodies;
    for (const [planetKey, planetData] of Object.entries(celestialBodies)) {
      if (planetKey === 'all') continue;
      
      const planet = planetData as any;
      if (planet && planet.ChartPosition) {
        planets.push({
          name: VEDIC_PLANETS[planetKey as keyof typeof VEDIC_PLANETS] || planetKey,
          longitude: planet.ChartPosition.Ecliptic.DecimalDegrees,
          latitude: 0, // Will be calculated separately if needed
          sign: getVedicSign(planet.ChartPosition.Ecliptic.DecimalDegrees),
          house: getHouseFromDegree(planet.ChartPosition.Horizon.DecimalDegrees),
          nakshatra: getNakshatra(planet.ChartPosition.Ecliptic.DecimalDegrees),
          isRetrograde: planet.isRetrograde || false
        });
      }
    }

    // Process Lunar Nodes (Rahu/Ketu)
    const celestialPoints = horoscope.CelestialPoints;
    if (celestialPoints.northnode) {
      const rahu = celestialPoints.northnode as any;
      planets.push({
        name: 'Rahu',
        longitude: rahu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(rahu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    if (celestialPoints.southnode) {
      const ketu = celestialPoints.southnode as any;
      planets.push({
        name: 'Ketu',
        longitude: ketu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(ketu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    // Extract house information
    const houses: HouseInfo[] = [];
    const houseData = horoscope.Houses;
    for (let i = 0; i < 12; i++) {
      if (houseData[i]) {
        const house = houseData[i] as any;
        houses.push({
          number: i + 1,
          sign: getVedicSign(house.ChartPosition.Ecliptic.DecimalDegrees),
          cusp: house.ChartPosition.Ecliptic.DecimalDegrees,
          lord: getHouseLord(getVedicSign(house.ChartPosition.Ecliptic.DecimalDegrees))
        });
      }
    }

    // Get ascendant, moon sign, and sun sign
    const ascendant = horoscope.Ascendant ? getVedicSign((horoscope.Ascendant as any).ChartPosition.Ecliptic.DecimalDegrees) : 'Unknown';
    const moonSign = celestialBodies.moon ? getVedicSign((celestialBodies.moon as any).ChartPosition.Ecliptic.DecimalDegrees) : 'Unknown';
    const sunSign = celestialBodies.sun ? getVedicSign((celestialBodies.sun as any).ChartPosition.Ecliptic.DecimalDegrees) : 'Unknown';

    // Get aspects
    const aspects = horoscope.Aspects.all || [];

    // Calculate Nakshatras for all planets
    const nakshatras = planets.map(planet => ({
      planet: planet.name,
      nakshatra: planet.nakshatra,
      longitude: planet.longitude
    }));

    // Calculate basic Dashas (simplified)
    const dashas = calculateBasicDashas(celestialBodies.moon ? (celestialBodies.moon as any).ChartPosition.Ecliptic.DecimalDegrees : 0);

    const birthChartData: BirthChartData = {
      ascendant,
      moonSign,
      sunSign,
      planets,
      houses,
      aspects,
      nakshatras,
      dashas
    };

    console.log('✅ Birth chart calculated successfully');
    return birthChartData;

  } catch (error) {
    console.error('❌ Error calculating birth chart:', error);
    throw new Error(`Failed to calculate birth chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Vedic zodiac sign from longitude
 */
function getVedicSign(longitude: number): string {
  const signIndex = Math.floor(longitude / 30);
  return VEDIC_SIGNS[signIndex] || 'Unknown';
}

/**
 * Get house number from horizon degree
 */
function getHouseFromDegree(horizonDegree: number): number {
  // Normalize to 0-360 range
  let degree = horizonDegree;
  while (degree < 0) degree += 360;
  while (degree >= 360) degree -= 360;
  
  // Calculate house (1-12)
  const house = Math.floor(degree / 30) + 1;
  return house > 12 ? house - 12 : house;
}

/**
 * Get Nakshatra from longitude
 */
function getNakshatra(longitude: number): string {
  // Each nakshatra spans 13.333... degrees (360/27)
  const nakshatraIndex = Math.floor(longitude / (360 / 27));
  return NAKSHATRAS[nakshatraIndex] || 'Unknown';
}

/**
 * Get house lord for a sign
 */
function getHouseLord(sign: string): string {
  const lords: { [key: string]: string } = {
    'Aries': 'Mars',
    'Taurus': 'Venus',
    'Gemini': 'Mercury',
    'Cancer': 'Moon',
    'Leo': 'Sun',
    'Virgo': 'Mercury',
    'Libra': 'Venus',
    'Scorpio': 'Mars',
    'Sagittarius': 'Jupiter',
    'Capricorn': 'Saturn',
    'Aquarius': 'Saturn',
    'Pisces': 'Jupiter'
  };
  return lords[sign] || 'Unknown';
}

/**
 * Calculate basic Dasha periods (simplified Vimshottari Dasha)
 */
function calculateBasicDashas(moonLongitude: number): any[] {
  // This is a simplified version - real Dasha calculations are much more complex
  const nakshatra = getNakshatra(moonLongitude);
  const nakshatraIndex = NAKSHATRAS.indexOf(nakshatra);
  
  // Vimshottari Dasha periods in years
  const dashaPeriods = [
    { planet: 'Ketu', years: 7 },
    { planet: 'Venus', years: 20 },
    { planet: 'Sun', years: 6 },
    { planet: 'Moon', years: 10 },
    { planet: 'Mars', years: 7 },
    { planet: 'Rahu', years: 18 },
    { planet: 'Jupiter', years: 16 },
    { planet: 'Saturn', years: 19 },
    { planet: 'Mercury', years: 17 }
  ];
  
  // Start from the nakshatra lord
  const startIndex = nakshatraIndex % 9;
  const dashas = [];
  
  for (let i = 0; i < 9; i++) {
    const index = (startIndex + i) % 9;
    dashas.push(dashaPeriods[index]);
  }
  
  return dashas;
}
