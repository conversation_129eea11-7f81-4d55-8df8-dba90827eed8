import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, canManageAdmins, hashPassword } from '@/lib/auth';
import { ApiResponse } from '@/types';

// GET - Get specific admin details (Super Admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ adminId: string }> }
) {
  try {
    // Check super admin authentication
    const admin = getAdminFromRequest(request);
    if (!canManageAdmins(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Super Admin access required'
      }, { status: 403 });
    }

    const { adminId } = await params;

    const targetAdmin = await prisma.admin.findUnique({
      where: { id: adminId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true,
        createdBy: true,
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (!targetAdmin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse<typeof targetAdmin>>({
      success: true,
      data: targetAdmin,
      message: 'Admin details retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching admin details:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// PATCH - Update admin (Super Admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ adminId: string }> }
) {
  try {
    // Check super admin authentication
    const admin = getAdminFromRequest(request);
    if (!canManageAdmins(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Super Admin access required'
      }, { status: 403 });
    }

    const { adminId } = await params;
    const updateData = await request.json();

    // Validate admin exists
    const targetAdmin = await prisma.admin.findUnique({
      where: { id: adminId }
    });

    if (!targetAdmin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin not found'
      }, { status: 404 });
    }

    // Prevent modifying own account through this endpoint
    if (adminId === admin!.adminId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Cannot modify your own admin account through this endpoint'
      }, { status: 400 });
    }

    // Prepare update data
    const allowedFields = ['name', 'email', 'role', 'isActive', 'password'];
    const filteredData: any = {};

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        if (key === 'password' && typeof value === 'string' && value.length > 0) {
          filteredData[key] = await hashPassword(value as string);
        } else if (key === 'email' && typeof value === 'string') {
          filteredData[key] = (value as string).toLowerCase();
        } else if (key === 'role' && !['admin', 'super_admin'].includes(value as string)) {
          return NextResponse.json<ApiResponse<null>>({
            success: false,
            error: 'Invalid role. Must be admin or super_admin'
          }, { status: 400 });
        } else if (key !== 'password') {
          filteredData[key] = value;
        }
      }
    }

    if (Object.keys(filteredData).length === 0) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'No valid fields to update'
      }, { status: 400 });
    }

    // Check for email uniqueness if email is being updated
    if (filteredData.email) {
      const existingAdmin = await prisma.admin.findUnique({
        where: { 
          email: filteredData.email,
          NOT: { id: adminId }
        }
      });

      if (existingAdmin) {
        return NextResponse.json<ApiResponse<null>>({
          success: false,
          error: 'Admin with this email already exists'
        }, { status: 400 });
      }
    }

    // Update admin
    const updatedAdmin = await prisma.admin.update({
      where: { id: adminId },
      data: filteredData,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true,
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json<ApiResponse<typeof updatedAdmin>>({
      success: true,
      data: updatedAdmin,
      message: 'Admin updated successfully'
    });

  } catch (error) {
    console.error('Error updating admin:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
