'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useLanguage } from '@/hooks/useLanguage';
import { DashboardData } from '@/types';
import { ZODIAC_INFO } from '@/utils/zodiac';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import ZodiacCard from '@/components/ZodiacCard';
import TranslatedText from '@/components/TranslatedText';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import PWAInstaller from '@/components/PWAInstaller';
import MobileNavigation from '@/components/MobileNavigation';
import { Settings, Star, Calendar, Clock, Palette, Hash, BookOpen, Heart, Briefcase, Activity, Gem, Users, Smile } from 'lucide-react';

export default function Dashboard() {
  const { user, isAuthenticated, loading: authLoading, getSessionTimeRemaining, isSessionExpired } = useAuth();
  const { language, setLanguage } = useLanguage();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'horoscope' | 'guide'>('horoscope');
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number>(0);
  const router = useRouter();

  // Update session time remaining every minute
  useEffect(() => {
    if (!isAuthenticated) return;

    const updateSessionTime = () => {
      const remaining = getSessionTimeRemaining();
      setSessionTimeRemaining(remaining);

      // If session expired, redirect to home
      if (remaining <= 0 || isSessionExpired()) {
        console.log('Session expired, redirecting to home');
        router.push('/');
        return;
      }
    };

    // Update immediately
    updateSessionTime();

    // Update every minute
    const interval = setInterval(updateSessionTime, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, getSessionTimeRemaining, isSessionExpired, router]);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/');
      return;
    }

    if (user) {
      fetchDashboardData();
    }
  }, [user, isAuthenticated, authLoading, router, language]);

  const fetchDashboardData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/dashboard?userId=${user.id}&language=${language}`);
      const data = await response.json();

      if (data.success) {
        console.log('📊 Dashboard data received:', data.data);
        console.log('🌐 Daily reading translations:', data.data.dailyReadingTranslations);
        setDashboardData(data.data);
      } else {
        setError(data.error || 'Failed to load dashboard data');
      }
    } catch (err) {
      console.error('Dashboard fetch error:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {
    // Update user preference in backend
    if (user) {
      try {
        await fetch('/api/dashboard', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId: user.id, language: newLanguage })
        });
      } catch (error) {
        console.error('Failed to update language preference:', error);
      }
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <LoadingSpinner message="Loading your cosmic insights..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title="Error Loading Dashboard"
          message={error}
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  if (!dashboardData || !user) {
    return null;
  }

  const zodiacInfo = ZODIAC_INFO[user.zodiacSign];

  // Fallback if zodiac info is not found
  if (!zodiacInfo) {
    console.error('Zodiac info not found for sign:', user.zodiacSign);
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title="Configuration Error"
          message="Unable to load zodiac information. Please contact support."
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Session Warning */}
      {sessionTimeRemaining <= 5 && sessionTimeRemaining > 0 && (
        <div className="bg-red-600/90 backdrop-blur-sm text-white px-4 py-2 text-center text-sm font-medium">
          ⚠️ Your session will expire in {sessionTimeRemaining} minute{sessionTimeRemaining !== 1 ? 's' : ''}. Please scan your QR code again to continue.
        </div>
      )}

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-3xl">{zodiacInfo.symbol}</div>
              <div>
                <h1 className="text-xl font-bold text-white">Welcome, {user.name}</h1>
                <p className="text-gray-300 text-sm">{zodiacInfo.name} • {zodiacInfo.dates}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Session Timer */}
              <div className="hidden md:flex items-center space-x-2 text-sm">
                <Clock size={16} className="text-yellow-400" />
                <span className={`font-medium ${sessionTimeRemaining <= 5 ? 'text-red-400' : sessionTimeRemaining <= 10 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {sessionTimeRemaining}m
                </span>
              </div>

              <LanguageSwitcher onLanguageChange={handleLanguageChange} />

              <button className="hidden md:block text-gray-300 hover:text-white transition-colors">
                <Settings size={20} />
              </button>

              <MobileNavigation
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-black/10 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'horoscope', label: 'Horoscope', icon: BookOpen },
              { id: 'guide', label: 'Daily Guide', icon: Clock }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-purple-400 text-white'
                    : 'border-transparent text-gray-300 hover:text-white'
                }`}
              >
                <Icon size={16} />
                <span><TranslatedText text={label} /></span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="max-w-6xl mx-auto px-4 py-8 pb-20 md:pb-8">
        {activeTab === 'horoscope' && (
          <div className="space-y-6">
            {/* Personal Horoscopes (Admin-created) */}
            {dashboardData.personalHoroscopes && dashboardData.personalHoroscopes.length > 0 ? (
              dashboardData.personalHoroscopes.map((horoscope: any, index: number) => (
                <div key={horoscope.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                    <Star className="mr-2 text-yellow-400" />
                    <TranslatedText text={horoscope.title} />
                  </h2>
                  <p className="text-gray-200 leading-relaxed text-lg">
                    <TranslatedText text={horoscope.content} />
                  </p>
                  <div className="mt-4 text-sm text-gray-400">
                    Created: {new Date(horoscope.createdAt).toLocaleDateString()}
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
                <BookOpen className="mx-auto mb-4 text-gray-400" size={48} />
                <h3 className="text-xl font-semibold text-white mb-2">
                  <TranslatedText text="No Personal Horoscope Available" />
                </h3>
                <p className="text-gray-300">
                  <TranslatedText text="Your personalized horoscope content will appear here once added by the admin." />
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'guide' && (
          dashboardData.dailyReading ? (
          <div className="space-y-6">
            {/* Daily Reading Header */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-2 flex items-center">
                <Clock className="mr-2 text-green-400" />
                <TranslatedText text="Today's Cosmic Guide" />
              </h2>
              <div className="flex items-center space-x-4 text-sm text-gray-300">
                <span>📅 {new Date(dashboardData.dailyReading.date).toLocaleDateString()}</span>
                <span className="flex items-center">
                  <Smile className="mr-1" size={16} />
                  {dashboardData.dailyReading.mood}
                </span>
              </div>
            </div>

            {/* Readings Grid */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* General Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Star className="mr-2 text-yellow-400" size={20} />
                  <TranslatedText text="General Reading" />
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.generalReading}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.generalReading,
                      si: dashboardData.dailyReadingTranslations.si?.generalReading
                    } : undefined}
                  />
                </p>
              </div>

              {/* Love Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Heart className="mr-2 text-pink-400" size={20} />
                  <TranslatedText text="Love & Relationships" />
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.loveReading}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.loveReading,
                      si: dashboardData.dailyReadingTranslations.si?.loveReading
                    } : undefined}
                  />
                </p>
              </div>

              {/* Career Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Briefcase className="mr-2 text-blue-400" size={20} />
                  <TranslatedText text="Career & Money" />
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.careerReading}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.careerReading,
                      si: dashboardData.dailyReadingTranslations.si?.careerReading
                    } : undefined}
                  />
                </p>
              </div>

              {/* Health Reading */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Activity className="mr-2 text-green-400" size={20} />
                  <TranslatedText text="Health & Wellness" />
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.healthReading}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.healthReading,
                      si: dashboardData.dailyReadingTranslations.si?.healthReading
                    } : undefined}
                  />
                </p>
              </div>
            </div>

            {/* Lucky Elements */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-4">
                <TranslatedText text="Today's Lucky Elements" />
              </h3>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Hash className="text-yellow-400 mx-auto mb-2" size={24} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    <TranslatedText text="Lucky Number" />
                  </h4>
                  <p className="text-2xl font-bold text-yellow-400">
                    {dashboardData.dailyReading.luckyNumber}
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Palette className="text-pink-400 mx-auto mb-2" size={24} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    <TranslatedText text="Lucky Color" />
                  </h4>
                  <p className="text-lg font-semibold text-pink-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyColor}
                      translations={dashboardData.dailyReadingTranslations ? {
                        en: dashboardData.dailyReadingTranslations.en?.luckyColor,
                        si: dashboardData.dailyReadingTranslations.si?.luckyColor
                      } : undefined}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Clock className="text-blue-400 mx-auto mb-2" size={24} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    <TranslatedText text="Lucky Time" />
                  </h4>
                  <p className="text-sm font-semibold text-blue-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyTime}
                      translations={dashboardData.dailyReadingTranslations ? {
                        en: dashboardData.dailyReadingTranslations.en?.luckyTime,
                        si: dashboardData.dailyReadingTranslations.si?.luckyTime
                      } : undefined}
                    />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <Gem className="text-purple-400 mx-auto mb-2" size={24} />
                  <h4 className="text-white font-semibold text-sm mb-1">
                    <TranslatedText text="Lucky Gem" />
                  </h4>
                  <p className="text-sm font-semibold text-purple-400">
                    <TranslatedText
                      text={dashboardData.dailyReading.luckyGem}
                      translations={dashboardData.dailyReadingTranslations ? {
                        en: dashboardData.dailyReadingTranslations.en?.luckyGem,
                        si: dashboardData.dailyReadingTranslations.si?.luckyGem
                      } : undefined}
                    />
                  </p>
                </div>
              </div>

              {/* Daily Advice */}
              <div className="bg-white/5 rounded-lg p-4 mb-4">
                <h4 className="text-white font-semibold mb-3 flex items-center">
                  <BookOpen className="mr-2 text-green-400" size={20} />
                  <TranslatedText text="Daily Advice" />
                </h4>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText
                    text={dashboardData.dailyReading.advice}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.advice,
                      si: dashboardData.dailyReadingTranslations.si?.advice
                    } : undefined}
                  />
                </p>
              </div>

              {/* Compatibility */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-semibold mb-3 flex items-center">
                  <Users className="mr-2 text-orange-400" size={20} />
                  <TranslatedText text="Compatible Signs Today" />
                </h4>
                <p className="text-orange-300 font-medium">
                  <TranslatedText
                    text={dashboardData.dailyReading.compatibility}
                    translations={dashboardData.dailyReadingTranslations ? {
                      en: dashboardData.dailyReadingTranslations.en?.compatibility,
                      si: dashboardData.dailyReadingTranslations.si?.compatibility
                    } : undefined}
                  />
                </p>
              </div>
            </div>
          </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
              <Clock className="mx-auto mb-4 text-gray-400" size={48} />
              <h3 className="text-xl font-semibold text-white mb-2">
                <TranslatedText text="No Daily Guide Available" />
              </h3>
              <p className="text-gray-300">
                <TranslatedText text="Your daily cosmic guide will appear here automatically. Please check back later." />
              </p>
            </div>
          )
        )}
      </main>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
