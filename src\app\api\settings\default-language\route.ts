import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse, LanguageCode } from '@/types';

// Get default language setting (public endpoint)
export async function GET(request: NextRequest) {
  try {
    // Get system settings
    const settings = await prisma.systemSettings.findFirst();

    const defaultLanguage: LanguageCode = settings?.defaultLanguage || 'si';

    return NextResponse.json<ApiResponse<{ defaultLanguage: LanguageCode }>>({
      success: true,
      data: { defaultLanguage },
      message: 'Default language retrieved successfully'
    });

  } catch (error) {
    console.error('Get default language error:', error);
    return NextResponse.json<ApiResponse<{ defaultLanguage: LanguageCode }>>({
      success: true,
      data: { defaultLanguage: 'si' }, // Fallback to Sinhala
      message: 'Default language retrieved (fallback)'
    });
  }
}
