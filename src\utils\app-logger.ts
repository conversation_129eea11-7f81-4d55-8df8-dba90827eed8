// Comprehensive App Logger for AstroConnect
// Logs all user actions, errors, API calls, and system events

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

export enum LogCategory {
  AUTH = 'AUTH',
  TRANSLATION = 'TRANSLATION',
  API = 'API',
  UI = 'UI',
  NAVIGATION = 'NAVIGATION',
  BIRTH_CHART = 'BIRTH_CHART',
  DAILY_GUIDE = 'DAILY_GUIDE',
  ADMIN = 'ADMIN',
  QR = 'QR',
  SYSTEM = 'SYSTEM'
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  stack?: string;
}

class AppLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
    this.log(LogLevel.INFO, LogCategory.SYSTEM, 'App Logger initialized', { sessionId: this.sessionId });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers() {
    // Catch unhandled errors
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.log(LogLevel.ERROR, LogCategory.SYSTEM, 'Unhandled Error', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error?.stack
        });
      });

      // Catch unhandled promise rejections
      window.addEventListener('unhandledrejection', (event) => {
        this.log(LogLevel.ERROR, LogCategory.SYSTEM, 'Unhandled Promise Rejection', {
          reason: event.reason,
          stack: event.reason?.stack
        });
      });
    }
  }

  log(level: LogLevel, category: LogCategory, message: string, data?: any, userId?: string) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      userId,
      sessionId: this.sessionId,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined
    };

    // Add to memory
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift(); // Remove oldest log
    }

    // Console output with colors
    this.outputToConsole(entry);

    // Send critical errors to server (if needed)
    if (level === LogLevel.CRITICAL || level === LogLevel.ERROR) {
      this.sendToServer(entry);
    }
  }

  private outputToConsole(entry: LogEntry) {
    const prefix = `[${entry.timestamp}] [${entry.level}] [${entry.category}]`;
    const style = this.getConsoleStyle(entry.level);
    
    if (entry.data) {
      console.groupCollapsed(`%c${prefix} ${entry.message}`, style);
      console.log('Data:', entry.data);
      if (entry.userId) console.log('User ID:', entry.userId);
      if (entry.url) console.log('URL:', entry.url);
      if (entry.stack) console.log('Stack:', entry.stack);
      console.groupEnd();
    } else {
      console.log(`%c${prefix} ${entry.message}`, style);
    }
  }

  private getConsoleStyle(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return 'color: #888; font-size: 11px;';
      case LogLevel.INFO:
        return 'color: #2196F3; font-weight: bold;';
      case LogLevel.WARN:
        return 'color: #FF9800; font-weight: bold;';
      case LogLevel.ERROR:
        return 'color: #F44336; font-weight: bold;';
      case LogLevel.CRITICAL:
        return 'color: #FFFFFF; background-color: #F44336; font-weight: bold; padding: 2px 4px;';
      default:
        return '';
    }
  }

  private async sendToServer(entry: LogEntry) {
    try {
      // Only send in production or when explicitly enabled
      if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true') {
        await fetch('/api/logs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(entry)
        });
      }
    } catch (error) {
      console.error('Failed to send log to server:', error);
    }
  }

  // Convenience methods
  debug(category: LogCategory, message: string, data?: any, userId?: string) {
    this.log(LogLevel.DEBUG, category, message, data, userId);
  }

  info(category: LogCategory, message: string, data?: any, userId?: string) {
    this.log(LogLevel.INFO, category, message, data, userId);
  }

  warn(category: LogCategory, message: string, data?: any, userId?: string) {
    this.log(LogLevel.WARN, category, message, data, userId);
  }

  error(category: LogCategory, message: string, data?: any, userId?: string) {
    this.log(LogLevel.ERROR, category, message, data, userId);
  }

  critical(category: LogCategory, message: string, data?: any, userId?: string) {
    this.log(LogLevel.CRITICAL, category, message, data, userId);
  }

  // Get logs for debugging
  getLogs(level?: LogLevel, category?: LogCategory): LogEntry[] {
    return this.logs.filter(log => {
      if (level && log.level !== level) return false;
      if (category && log.category !== category) return false;
      return true;
    });
  }

  // Clear logs
  clearLogs() {
    this.logs = [];
    this.info(LogCategory.SYSTEM, 'Logs cleared');
  }

  // Export logs as JSON
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Create singleton instance
export const appLogger = new AppLogger();

// Helper functions for common logging scenarios
export const logUserAction = (action: string, data?: any, userId?: string) => {
  appLogger.info(LogCategory.UI, `User Action: ${action}`, data, userId);
};

export const logAPICall = (endpoint: string, method: string, data?: any, userId?: string) => {
  appLogger.info(LogCategory.API, `API Call: ${method} ${endpoint}`, data, userId);
};

export const logAPIResponse = (endpoint: string, success: boolean, data?: any, userId?: string) => {
  const level = success ? LogLevel.INFO : LogLevel.ERROR;
  appLogger.log(level, LogCategory.API, `API Response: ${endpoint}`, data, userId);
};

export const logTranslation = (text: string, fromLang: string, toLang: string, success: boolean, userId?: string) => {
  const level = success ? LogLevel.DEBUG : LogLevel.WARN;
  appLogger.log(level, LogCategory.TRANSLATION, `Translation: ${fromLang} -> ${toLang}`, {
    text: text.substring(0, 50) + '...',
    success
  }, userId);
};

export const logNavigation = (from: string, to: string, userId?: string) => {
  appLogger.info(LogCategory.NAVIGATION, `Navigation: ${from} -> ${to}`, undefined, userId);
};

export const logError = (error: Error, context: string, userId?: string) => {
  appLogger.error(LogCategory.SYSTEM, `Error in ${context}: ${error.message}`, {
    stack: error.stack,
    context
  }, userId);
};
