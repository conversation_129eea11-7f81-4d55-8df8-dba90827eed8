import { v4 as uuidv4 } from 'uuid';
import QRCode from 'qrcode';

export function generateQRToken(): string {
  return uuidv4();
}

export function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {
  return `${baseUrl}/qr/${token}`;
}

export async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {
  const url = generateQRUrl(token, baseUrl);
  try {
    const qrCodeDataUrl = await QRCode.toDataURL(url, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    return qrCodeDataUrl;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

export function extractTokenFromUrl(url: string): string | null {
  try {
    // Handle empty or invalid input
    if (!url || typeof url !== 'string' || url.trim().length === 0) {
      return null;
    }

    // Try to parse as URL
    const urlObj = new URL(url);

    // Check if it's our domain (for security)
    const expectedHosts = [
      'localhost:3000',
      'localhost:3001',
      process.env.NEXT_PUBLIC_APP_URL?.replace(/^https?:\/\//, '')
    ].filter(Boolean);

    const isOurDomain = expectedHosts.some(host =>
      urlObj.host === host || urlObj.hostname === host?.split(':')[0]
    );

    if (!isOurDomain) {
      console.log('QR code is not for our domain:', urlObj.host);
      return null;
    }

    // Check for query parameter format: /auth?token=xxx
    const tokenParam = urlObj.searchParams.get('token');
    if (tokenParam && tokenParam.trim().length > 0) {
      return tokenParam.trim();
    }

    // Check for path format: /qr/xxx
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
    const qrIndex = pathParts.indexOf('qr');

    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {
      const token = pathParts[qrIndex + 1];
      return token && token.trim().length > 0 ? token.trim() : null;
    }

    return null;
  } catch (error) {
    // If URL parsing fails, it's not a valid URL
    console.log('Not a valid URL for token extraction:', url);
    return null;
  }
}

export function isValidQRToken(token: string): boolean {
  // Allow both UUID format and simple alphanumeric tokens for testing
  if (!token || typeof token !== 'string') {
    return false;
  }

  // UUID v4 regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  // Simple alphanumeric token pattern (for testing)
  const simpleTokenRegex = /^[a-zA-Z0-9]{3,50}$/;

  return uuidRegex.test(token) || simpleTokenRegex.test(token);
}

export function detectQRCodeType(content: string): string {
  if (!content || typeof content !== 'string') {
    return 'invalid';
  }

  const trimmedContent = content.trim();

  // Check if it's a URL
  if (trimmedContent.startsWith('http://') || trimmedContent.startsWith('https://')) {
    try {
      const url = new URL(trimmedContent);

      // Check if it's our app
      const expectedHosts = [
        'localhost:3000',
        'localhost:3001',
        process.env.NEXT_PUBLIC_APP_URL?.replace(/^https?:\/\//, '')
      ].filter(Boolean);

      const isOurDomain = expectedHosts.some(host =>
        url.host === host || url.hostname === host?.split(':')[0]
      );

      if (isOurDomain) {
        return 'astroconnect-url';
      }

      // Common website patterns
      if (url.hostname.includes('google.com')) return 'google-url';
      if (url.hostname.includes('facebook.com')) return 'facebook-url';
      if (url.hostname.includes('instagram.com')) return 'instagram-url';
      if (url.hostname.includes('twitter.com') || url.hostname.includes('x.com')) return 'twitter-url';
      if (url.hostname.includes('youtube.com')) return 'youtube-url';

      return 'website-url';
    } catch {
      return 'invalid-url';
    }
  }

  // Check for WiFi QR codes
  if (trimmedContent.startsWith('WIFI:')) {
    return 'wifi';
  }

  // Check for contact/vCard
  if (trimmedContent.startsWith('BEGIN:VCARD') || trimmedContent.startsWith('MECARD:')) {
    return 'contact';
  }

  // Check for email
  if (trimmedContent.startsWith('mailto:')) {
    return 'email';
  }

  // Check for phone number
  if (trimmedContent.startsWith('tel:') || /^\+?[\d\s\-\(\)]+$/.test(trimmedContent)) {
    return 'phone';
  }

  // Check for SMS
  if (trimmedContent.startsWith('sms:') || trimmedContent.startsWith('smsto:')) {
    return 'sms';
  }

  // Check for plain text
  if (trimmedContent.length > 0 && trimmedContent.length < 1000) {
    return 'text';
  }

  return 'unknown';
}
