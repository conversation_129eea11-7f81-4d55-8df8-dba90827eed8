import { v4 as uuidv4 } from 'uuid';
import QRCode from 'qrcode';

export function generateQRToken(): string {
  return uuidv4();
}

export function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {
  return `${baseUrl}/qr/${token}`;
}

export async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {
  const url = generateQRUrl(token, baseUrl);
  try {
    const qrCodeDataUrl = await QRCode.toDataURL(url, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    return qrCodeDataUrl;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

export function extractTokenFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const qrIndex = pathParts.indexOf('qr');
    
    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {
      return pathParts[qrIndex + 1];
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting token from URL:', error);
    return null;
  }
}

export function isValidQRToken(token: string): boolean {
  // UUID v4 regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(token);
}
