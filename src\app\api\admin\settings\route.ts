import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse, LanguageCode } from '@/types';

// Get system settings
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    // Get or create system settings
    let settings = await prisma.systemSettings.findFirst();
    
    if (!settings) {
      // Create default settings if none exist
      settings = await prisma.systemSettings.create({
        data: {
          defaultLanguage: 'en'
        }
      });
    }

    return NextResponse.json<ApiResponse<typeof settings>>({
      success: true,
      data: settings,
      message: 'System settings retrieved successfully'
    });

  } catch (error) {
    console.error('Get system settings error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// Update system settings
export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { defaultLanguage } = await request.json();

    if (!defaultLanguage || !['en', 'si'].includes(defaultLanguage)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid default language. Must be "en" or "si"'
      }, { status: 400 });
    }

    // Get or create system settings
    let settings = await prisma.systemSettings.findFirst();
    
    if (!settings) {
      // Create new settings
      settings = await prisma.systemSettings.create({
        data: {
          defaultLanguage: defaultLanguage as LanguageCode
        }
      });
    } else {
      // Update existing settings
      settings = await prisma.systemSettings.update({
        where: { id: settings.id },
        data: {
          defaultLanguage: defaultLanguage as LanguageCode
        }
      });
    }

    return NextResponse.json<ApiResponse<typeof settings>>({
      success: true,
      data: settings,
      message: 'System settings updated successfully'
    });

  } catch (error) {
    console.error('Update system settings error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
