// Import Prisma generated types
import {
  User as PrismaUser,
  ZodiacSign,
  LanguageCode,
  HoroscopeType,
  Horoscope as PrismaHoroscope,
  QrCodeMapping as PrismaQrCodeMapping,
  TranslationCache as PrismaTranslationCache,
  BirthChart as PrismaBirthChart
} from '@prisma/client';

// Re-export Prisma enums and types
export { ZodiacSign, LanguageCode, HoroscopeType };

// Use Prisma types with proper naming
export type User = PrismaUser;
export type Horoscope = PrismaHoroscope;
export type QRCodeMapping = PrismaQrCodeMapping;
export type TranslationCache = PrismaTranslationCache;
export type BirthChart = PrismaBirthChart;

// Extended types for API responses
export interface UserWithMappings extends User {
  qrCodeMappings: QRCodeMapping[];
}

export interface UserWithSession extends User {
  sessionExpiry?: string;
  sessionStarted?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Dashboard Data
export interface DashboardData {
  user: User;
  todayHoroscope: Horoscope;
  weeklyHoroscope: Horoscope;
  monthlyHoroscope: Horoscope;
  personalHoroscopes?: any[]; // Legacy personal horoscopes
  birthChart?: BirthChart; // New calculated birth chart system
  dailyReading?: any; // DailyZodiacReading type
  dailyReadingTranslations?: {
    en?: any; // DailyZodiacReading type
    si?: any; // DailyZodiacReading type
  };
}

// Language Context
export interface LanguageContextType {
  language: LanguageCode;
  setLanguage: (lang: LanguageCode) => void;
  translate: (text: string) => Promise<string>;
  isTranslating: boolean;
  isInitialized?: boolean;
}

// QR Scanner Props
export interface QRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onScanError?: (error: string) => void;
  width?: number;
  height?: number;
}
