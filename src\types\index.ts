// Import Prisma generated types
import {
  User as PrismaUser,
  ZodiacSign,
  LanguageCode,
  HoroscopeType,
  Horoscope as PrismaHoroscope,
  DailyGuide as PrismaDailyGuide,
  QrCodeMapping as PrismaQrCodeMapping,
  TranslationCache as PrismaTranslationCache
} from '@prisma/client';

// Re-export Prisma enums and types
export { ZodiacSign, LanguageCode, HoroscopeType };

// Use Prisma types with proper naming
export type User = PrismaUser;
export type Horoscope = PrismaHoroscope;
export type DailyGuide = PrismaDailyGuide;
export type QRCodeMapping = PrismaQrCodeMapping;
export type TranslationCache = PrismaTranslationCache;

// Extended types for API responses
export interface UserWithMappings extends User {
  qrCodeMappings: QRCodeMapping[];
}

export interface UserWithSession extends User {
  sessionExpiry?: string;
  sessionStarted?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Dashboard Data
export interface DashboardData {
  user: User;
  todayHoroscope: Horoscope;
  weeklyHoroscope: Horoscope;
  monthlyHoroscope: Horoscope;
  dailyGuide: DailyGuide;
}

// Language Context
export interface LanguageContextType {
  language: LanguageCode;
  setLanguage: (lang: LanguageCode) => void;
  translate: (text: string) => Promise<string>;
  isTranslating: boolean;
}

// QR Scanner Props
export interface QRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onScanError?: (error: string) => void;
  width?: number;
  height?: number;
}
