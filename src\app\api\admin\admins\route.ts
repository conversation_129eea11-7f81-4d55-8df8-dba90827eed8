import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireSuperAdminAuth, canManageAdmins, hashPassword } from '@/lib/auth';
import { ApiResponse } from '@/types';

// GET - List all admins (Super Admin only)
export async function GET(request: NextRequest) {
  try {
    // Check super admin authentication
    const admin = getAdminFromRequest(request);
    if (!canManageAdmins(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Super Admin access required'
      }, { status: 403 });
    }

    // Get all admins with creator information
    const admins = await prisma.admin.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true,
        createdBy: true,
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json<ApiResponse<typeof admins>>({
      success: true,
      data: admins,
      message: 'Admins retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching admins:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new admin (Super Admin only)
export async function POST(request: NextRequest) {
  try {
    // Check super admin authentication
    const admin = getAdminFromRequest(request);
    if (!canManageAdmins(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Super Admin access required'
      }, { status: 403 });
    }

    const { email, password, name, role } = await request.json();

    // Validate input
    if (!email || !password || !name || !role) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Email, password, name, and role are required'
      }, { status: 400 });
    }

    // Validate role
    if (!['admin', 'super_admin'].includes(role)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid role. Must be admin or super_admin'
      }, { status: 400 });
    }

    // Check if email already exists
    const existingAdmin = await prisma.admin.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (existingAdmin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin with this email already exists'
      }, { status: 400 });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create new admin
    const newAdmin = await prisma.admin.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
        name,
        role,
        isActive: true,
        createdBy: admin!.adminId
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json<ApiResponse<typeof newAdmin>>({
      success: true,
      data: newAdmin,
      message: 'Admin created successfully'
    });

  } catch (error) {
    console.error('Error creating admin:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Delete admin (Super Admin only)
export async function DELETE(request: NextRequest) {
  try {
    // Check super admin authentication
    const admin = getAdminFromRequest(request);
    if (!canManageAdmins(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Super Admin access required'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const adminId = searchParams.get('adminId');

    if (!adminId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin ID is required'
      }, { status: 400 });
    }

    // Prevent self-deletion
    if (adminId === admin!.adminId) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Cannot delete your own admin account'
      }, { status: 400 });
    }

    // Check if admin exists
    const targetAdmin = await prisma.admin.findUnique({
      where: { id: adminId }
    });

    if (!targetAdmin) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Admin not found'
      }, { status: 404 });
    }

    // Delete the admin
    await prisma.admin.delete({
      where: { id: adminId }
    });

    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({
      success: true,
      data: { deleted: true },
      message: 'Admin deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting admin:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
