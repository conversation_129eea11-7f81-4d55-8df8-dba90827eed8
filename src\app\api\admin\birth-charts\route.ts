import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAdminToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    console.log('📊 Admin fetching birth charts');

    // Get all birth charts with user information
    const birthCharts = await prisma.birthChart.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            zodiacSign: true,
            birthDate: true,
            birthTime: true,
            birthPlace: true
          }
        }
      },
      orderBy: {
        calculatedAt: 'desc'
      }
    });

    console.log(`✅ Found ${birthCharts.length} birth charts`);

    return NextResponse.json({
      success: true,
      data: birthCharts
    });

  } catch (error) {
    console.error('❌ Error fetching birth charts:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch birth charts' 
      },
      { status: 500 }
    );
  }
}
