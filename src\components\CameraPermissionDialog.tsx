'use client';

import React, { useState, useEffect } from 'react';
import { Camera, Shield, AlertTriangle, CheckCircle, XCircle, RefreshCw, Info, ExternalLink } from 'lucide-react';
import { 
  checkCameraPermission, 
  requestCameraPermission, 
  checkCameraCapabilities,
  type CameraPermissionResult,
  type CameraCapabilities 
} from '@/lib/cameraPermissions';

interface CameraPermissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPermissionGranted: (stream: MediaStream) => void;
  onPermissionDenied: (error: string) => void;
}

export default function CameraPermissionDialog({
  isOpen,
  onClose,
  onPermissionGranted,
  onPermissionDenied
}: CameraPermissionDialogProps) {
  const [permissionState, setPermissionState] = useState<CameraPermissionResult | null>(null);
  const [capabilities, setCapabilities] = useState<CameraCapabilities | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isRequesting, setIsRequesting] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  // Check permissions when dialog opens
  useEffect(() => {
    if (isOpen) {
      checkPermissions();
      checkCapabilities();
    }
  }, [isOpen]);

  const checkPermissions = async () => {
    setIsChecking(true);
    try {
      const result = await checkCameraPermission();
      setPermissionState(result);
      
      if (result.granted) {
        // If already granted, try to get stream immediately
        await handleRequestPermission();
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      setPermissionState({
        granted: false,
        state: 'unknown',
        error: 'Failed to check camera permissions',
        needsUserAction: true
      });
    } finally {
      setIsChecking(false);
    }
  };

  const checkCapabilities = async () => {
    try {
      const caps = await checkCameraCapabilities();
      setCapabilities(caps);
    } catch (error) {
      console.error('Error checking capabilities:', error);
    }
  };

  const handleRequestPermission = async () => {
    setIsRequesting(true);
    try {
      const result = await requestCameraPermission();
      setPermissionState(result);
      
      if (result.granted) {
        // Get the actual stream
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: { ideal: 'environment' },
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          }
        });
        onPermissionGranted(stream);
        onClose();
      } else {
        onPermissionDenied(result.error || 'Camera permission denied');
        if (result.needsUserAction) {
          setShowInstructions(true);
        }
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to request camera permission';
      setPermissionState({
        granted: false,
        state: 'denied',
        error: errorMessage,
        needsUserAction: true
      });
      onPermissionDenied(errorMessage);
      setShowInstructions(true);
    } finally {
      setIsRequesting(false);
    }
  };

  const getStatusIcon = () => {
    if (isChecking || isRequesting) {
      return <RefreshCw className="w-6 h-6 text-blue-400 animate-spin" />;
    }
    
    if (!permissionState) {
      return <Camera className="w-6 h-6 text-gray-400" />;
    }

    switch (permissionState.state) {
      case 'granted':
        return <CheckCircle className="w-6 h-6 text-green-400" />;
      case 'denied':
        return <XCircle className="w-6 h-6 text-red-400" />;
      case 'prompt':
        return <AlertTriangle className="w-6 h-6 text-yellow-400" />;
      default:
        return <Shield className="w-6 h-6 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    if (isChecking) return 'Checking camera permissions...';
    if (isRequesting) return 'Requesting camera access...';
    
    if (!permissionState) return 'Ready to check camera access';
    
    if (permissionState.granted) {
      return 'Camera access granted! Starting camera...';
    }
    
    return permissionState.error || 'Camera access required';
  };

  const getStatusColor = () => {
    if (isChecking || isRequesting) return 'text-blue-300';
    if (!permissionState) return 'text-gray-300';
    
    switch (permissionState.state) {
      case 'granted':
        return 'text-green-300';
      case 'denied':
        return 'text-red-300';
      case 'prompt':
        return 'text-yellow-300';
      default:
        return 'text-gray-300';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-600">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Camera className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-bold text-white">Camera Access</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Status */}
        <div className="flex items-center space-x-3 mb-6">
          {getStatusIcon()}
          <div>
            <p className={`font-medium ${getStatusColor()}`}>
              {getStatusMessage()}
            </p>
            {capabilities && (
              <p className="text-sm text-gray-400 mt-1">
                {capabilities.hasCamera 
                  ? `${capabilities.cameraCount} camera(s) detected`
                  : 'No cameras found'
                }
              </p>
            )}
          </div>
        </div>

        {/* Capabilities Info */}
        {capabilities && !capabilities.isSecureContext && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-300 font-medium">Insecure Context</p>
                <p className="text-red-200 text-sm mt-1">
                  Camera access requires HTTPS or localhost. Please access this site securely.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Browser Support Info */}
        {capabilities && !capabilities.browserInfo.isSupported && (
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Info className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-yellow-300 font-medium">Browser Compatibility</p>
                <p className="text-yellow-200 text-sm mt-1">
                  Your browser ({capabilities.browserInfo.name} {capabilities.browserInfo.version}) may not fully support camera access.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        {showInstructions && permissionState?.browserInstructions && (
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-2">
              <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-blue-300 font-medium">Enable Camera Access</p>
                <div className="text-blue-200 text-sm mt-2 whitespace-pre-line">
                  {permissionState.browserInstructions}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          {!permissionState?.granted && capabilities?.isSecureContext && capabilities?.hasCamera && (
            <button
              onClick={handleRequestPermission}
              disabled={isRequesting || isChecking}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
            >
              {isRequesting ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Requesting...</span>
                </>
              ) : (
                <>
                  <Camera className="w-4 h-4" />
                  <span>Allow Camera Access</span>
                </>
              )}
            </button>
          )}
          
          {permissionState?.state === 'denied' && permissionState?.needsUserAction && (
            <button
              onClick={() => setShowInstructions(!showInstructions)}
              className="flex-1 bg-gray-600 hover:bg-gray-500 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              <Info className="w-4 h-4" />
              <span>{showInstructions ? 'Hide' : 'Show'} Instructions</span>
            </button>
          )}
          
          <button
            onClick={checkPermissions}
            disabled={isChecking}
            className="bg-gray-600 hover:bg-gray-500 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
            title="Refresh permission status"
          >
            <RefreshCw className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
          </button>
          
          <button
            onClick={onClose}
            className="bg-gray-600 hover:bg-gray-500 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            Cancel
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-4 text-center">
          <p className="text-gray-400 text-sm">
            We need camera access to scan QR codes. Your privacy is protected - we don't store any camera data.
          </p>
        </div>
      </div>
    </div>
  );
}
