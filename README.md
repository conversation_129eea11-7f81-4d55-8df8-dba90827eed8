# AstroConnect - Personal Horoscope & Daily Guide

AstroConnect is a modern web application that provides personalized horoscope readings and daily guidance through QR code authentication. Built with Next.js, TypeScript, and Supabase, it offers a seamless experience for users to access their cosmic insights.

## ✨ Features

### 🔮 Core Features
- **QR Code Authentication**: Secure, passwordless access using personalized QR codes
- **Daily Horoscopes**: Personalized daily, weekly, and monthly predictions
- **Lucky Guidance**: Daily lucky numbers, colors, and optimal times
- **Multi-language Support**: English and Sinhala with AI-powered translation
- **Mobile-First Design**: Responsive design optimized for mobile devices
- **Progressive Web App**: Installable with offline capabilities

### 🛡️ Security & Performance
- **Rate Limiting**: API protection with configurable limits
- **Input Validation**: Comprehensive security measures
- **HTTPS Enforcement**: SSL/TLS encryption
- **Caching Strategy**: Optimized performance with translation caching
- **Health Monitoring**: Built-in health checks and monitoring

### 👨‍💼 Admin Features
- **User Management**: Create, edit, and manage user accounts
- **Content Management**: Manage horoscope content and daily guides
- **Analytics Dashboard**: Track user engagement and QR scans
- **Multi-language Content**: Manage content in multiple languages

## 🚀 Quick Start

### Prerequisites
- Node.js 18.x or higher
- npm or yarn
- Supabase account
- Gemini API key (for translations)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd astroconnect
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your configuration:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   GEMINI_API_KEY=your_gemini_api_key
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up the database**
   - Create a new Supabase project
   - Run the SQL schema from `database/schema.sql`
   - Optionally load sample data from `database/sample_data.sql`

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
astroconnect/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── api/            # API routes
│   │   ├── dashboard/      # User dashboard
│   │   ├── admin/          # Admin panel
│   │   └── qr/             # QR authentication
│   ├── components/         # React components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility libraries
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Utility functions
├── database/               # Database schema and migrations
├── scripts/                # Deployment and utility scripts
├── public/                 # Static assets
└── docs/                   # Documentation
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Modern icon library
- **Framer Motion**: Animation library

### Backend
- **Next.js API Routes**: Serverless API endpoints
- **Supabase**: PostgreSQL database and authentication
- **Gemini API**: AI-powered translation service

### Development Tools
- **Jest**: Testing framework
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Docker**: Containerization

## 📱 Usage

### For Users
1. **Receive QR Code**: Get your personalized QR code from an admin
2. **Scan QR Code**: Use the app to scan your QR code for instant access
3. **View Dashboard**: Access your personalized horoscope and daily guidance
4. **Language Toggle**: Switch between English and Sinhala
5. **Install App**: Add to home screen for quick access

### For Admins
1. **Access Admin Panel**: Navigate to `/admin`
2. **Manage Users**: Create and manage user accounts
3. **Content Management**: Update horoscope content and daily guides
4. **Analytics**: Monitor user engagement and system health

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## 🚀 Deployment

### Docker Deployment (Recommended)

1. **Build and deploy**
   ```bash
   docker-compose up -d
   ```

2. **With SSL (Production)**
   ```bash
   docker-compose --profile production up -d
   ```

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start in production mode**
   ```bash
   npm start
   ```

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | Yes |
| `GEMINI_API_KEY` | Google Gemini API key | Yes |
| `NEXT_PUBLIC_APP_URL` | Application base URL | Yes |

### Database Configuration

The application uses Supabase (PostgreSQL) with the following tables:
- `users` - User profiles and authentication
- `qr_code_mappings` - QR token to user mappings
- `horoscopes` - Horoscope content (daily/weekly/monthly)
- `daily_guides` - Daily guidance with lucky elements
- `translation_cache` - Cached translations

## 🔒 Security

- **HTTPS Enforcement**: All traffic encrypted
- **Rate Limiting**: API protection against abuse
- **Input Validation**: Comprehensive input sanitization
- **CORS Configuration**: Proper cross-origin resource sharing
- **Row Level Security**: Database-level access control
- **Environment Variables**: Sensitive data protection

## 🌐 API Documentation

### Authentication
- `POST /api/auth/qr` - Authenticate with QR token

### Dashboard
- `GET /api/dashboard` - Get user dashboard data
- `POST /api/dashboard` - Update user preferences

### Translation
- `POST /api/translate` - Translate text using Gemini API

### Admin
- `GET /api/admin/users` - List users
- `POST /api/admin/users` - Create user
- `DELETE /api/admin/users` - Delete user
- `GET /api/admin/horoscopes` - List horoscopes
- `POST /api/admin/horoscopes` - Create horoscope

### Health
- `GET /api/health` - Application health check

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` directory
- **Issues**: Report bugs via GitHub Issues
- **Health Check**: Monitor `/api/health` endpoint
- **Logs**: Check application logs for debugging

## 🙏 Acknowledgments

- **Supabase**: For the excellent database and authentication platform
- **Google Gemini**: For AI-powered translation capabilities
- **Next.js Team**: For the amazing React framework
- **Tailwind CSS**: For the utility-first CSS framework
