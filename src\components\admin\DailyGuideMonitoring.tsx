'use client';

import { useState, useEffect } from 'react';
import { Clock, RefreshCw, Calendar, CheckCircle, AlertCircle, Play, Eye, Zap } from 'lucide-react';
import { ZODIAC_INFO } from '@/utils/zodiac';

interface SchedulerStatus {
  isRunning: boolean;
  lastGeneratedDate: string | null;
  nextCheck: string;
}

interface DailyGuideData {
  scheduler: SchedulerStatus;
  todayReadingsCount: number;
  isComplete: boolean;
  recentReadings: Record<string, { count: number; signs: string[] }>;
  serverTime: string;
  localTime: string;
}

export default function DailyGuideMonitoring() {
  const [data, setData] = useState<DailyGuideData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [viewingReadings, setViewingReadings] = useState<any[]>([]);
  const [showReadings, setShowReadings] = useState(false);

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchStatus = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/scheduler?action=status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      }
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateNow = async () => {
    if (generating) return;
    
    setGenerating(true);
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'generate',
          date: selectedDate
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`Successfully generated ${result.data.count} daily readings for ${result.data.date}`);
        fetchStatus(); // Refresh status
      } else {
        alert('Error: ' + result.error);
      }
    } catch (error) {
      console.error('Error generating readings:', error);
      alert('Error generating readings. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  const handleViewReadings = async (date: string) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/scheduler?action=readings&date=${date}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      if (result.success) {
        setViewingReadings(result.data.readings);
        setShowReadings(true);
      }
    } catch (error) {
      console.error('Error fetching readings:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="animate-spin text-purple-400" size={32} />
        <span className="ml-2 text-white">Loading scheduler status...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto mb-4 text-red-400" size={48} />
        <h3 className="text-xl font-semibold text-white mb-2">Unable to Load Status</h3>
        <p className="text-gray-300">Failed to fetch scheduler status.</p>
        <button 
          onClick={fetchStatus}
          className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Automated Daily Guides</h2>
          <p className="text-gray-300">
            Daily guides are automatically generated using Gemini AI for all zodiac signs
          </p>
        </div>
        <button
          onClick={fetchStatus}
          className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
        >
          <RefreshCw size={16} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Scheduler Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Scheduler Status</h3>
            <div className={`w-3 h-3 rounded-full ${data.scheduler.isRunning ? 'bg-green-400' : 'bg-red-400'}`} />
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-300">Status:</span>
              <span className={data.scheduler.isRunning ? 'text-green-400' : 'text-red-400'}>
                {data.scheduler.isRunning ? 'Running' : 'Stopped'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Next Check:</span>
              <span className="text-white">{data.scheduler.nextCheck}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Last Generated:</span>
              <span className="text-white">
                {data.scheduler.lastGeneratedDate || 'Never'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Today's Status</h3>
            {data.isComplete ? (
              <CheckCircle className="text-green-400" size={20} />
            ) : (
              <AlertCircle className="text-yellow-400" size={20} />
            )}
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-300">Generated:</span>
              <span className="text-white">{data.todayReadingsCount}/12</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Status:</span>
              <span className={data.isComplete ? 'text-green-400' : 'text-yellow-400'}>
                {data.isComplete ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-3">
              <div 
                className={`h-2 rounded-full ${data.isComplete ? 'bg-green-400' : 'bg-yellow-400'}`}
                style={{ width: `${(data.todayReadingsCount / 12) * 100}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-lg font-semibold text-white mb-4">Manual Generation</h3>
          <div className="space-y-3">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            />
            <button
              onClick={handleGenerateNow}
              disabled={generating}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2"
            >
              {generating ? (
                <>
                  <RefreshCw className="animate-spin" size={16} />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Zap size={16} />
                  <span>Generate Now</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Recent Readings */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Daily Guides (Last 7 Days)</h3>
        <div className="space-y-3">
          {Object.entries(data.recentReadings)
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
            .slice(0, 7)
            .map(([date, info]) => (
              <div key={date} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Calendar size={16} className="text-gray-400" />
                  <span className="text-white font-medium">
                    {new Date(date).toLocaleDateString()}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    info.count === 12 ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {info.count}/12 signs
                  </span>
                </div>
                <button
                  onClick={() => handleViewReadings(date)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                >
                  <Eye size={14} />
                  <span>View</span>
                </button>
              </div>
            ))}
        </div>
      </div>

      {/* Readings Modal */}
      {showReadings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">Daily Readings</h3>
              <button
                onClick={() => setShowReadings(false)}
                className="text-gray-400 hover:text-white"
              >
                ×
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {viewingReadings.map((reading) => (
                <div key={reading.id} className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xl">{ZODIAC_INFO[reading.zodiacSign]?.symbol}</span>
                    <span className="text-white font-semibold">
                      {ZODIAC_INFO[reading.zodiacSign]?.name}
                    </span>
                  </div>
                  <p className="text-gray-300 text-sm line-clamp-3">
                    {reading.advice}
                  </p>
                  <div className="mt-2 text-xs text-gray-400">
                    Lucky: {reading.luckyNumber} • {reading.luckyColor}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
