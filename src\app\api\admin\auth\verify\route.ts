import { NextRequest, NextResponse } from 'next/server';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const admin = getAdminFromRequest(request);

    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    return NextResponse.json<ApiResponse<{
      admin: {
        id: string;
        email: string;
        name: string;
        role: string;
      };
    }>>({
      success: true,
      data: {
        admin: {
          id: admin!.adminId,
          email: admin!.email,
          name: admin!.name,
          role: admin!.role
        }
      },
      message: 'Admin session valid'
    });

  } catch (error) {
    console.error('Admin verification error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
