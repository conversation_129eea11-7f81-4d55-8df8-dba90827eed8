'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import ConfirmationDialog, { ConfirmationDialogProps } from '@/components/ConfirmationDialog';

interface DialogOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'default' | 'danger' | 'success' | 'warning' | 'info';
}

interface DialogContextType {
  showConfirmation: (options: DialogOptions) => Promise<boolean>;
  showAlert: (title: string, message: string, type?: DialogOptions['type']) => Promise<void>;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

interface DialogState {
  isOpen: boolean;
  options: DialogOptions;
  resolve: ((value: boolean) => void) | null;
  loading: boolean;
}

export function DialogProvider({ children }: { children: React.ReactNode }) {
  const [dialogState, setDialogState] = useState<DialogState>({
    isOpen: false,
    options: { title: '', message: '' },
    resolve: null,
    loading: false,
  });

  const showConfirmation = useCallback((options: DialogOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogState({
        isOpen: true,
        options: {
          confirmText: 'Confirm',
          cancelText: 'Cancel',
          type: 'default',
          ...options,
        },
        resolve,
        loading: false,
      });
    });
  }, []);

  const showAlert = useCallback((title: string, message: string, type: DialogOptions['type'] = 'info'): Promise<void> => {
    return new Promise((resolve) => {
      setDialogState({
        isOpen: true,
        options: {
          title,
          message,
          confirmText: 'OK',
          cancelText: '', // Hide cancel button for alerts
          type,
        },
        resolve: (value: boolean) => resolve(), // Convert boolean to void
        loading: false,
      });
    });
  }, []);

  const handleConfirm = useCallback(() => {
    if (dialogState.resolve) {
      setDialogState(prev => ({ ...prev, loading: true }));
      
      // Small delay to show loading state
      setTimeout(() => {
        dialogState.resolve!(true);
        setDialogState({
          isOpen: false,
          options: { title: '', message: '' },
          resolve: null,
          loading: false,
        });
      }, 100);
    }
  }, [dialogState.resolve]);

  const handleCancel = useCallback(() => {
    if (dialogState.resolve) {
      dialogState.resolve(false);
      setDialogState({
        isOpen: false,
        options: { title: '', message: '' },
        resolve: null,
        loading: false,
      });
    }
  }, [dialogState.resolve]);

  const contextValue: DialogContextType = {
    showConfirmation,
    showAlert,
  };

  return (
    <DialogContext.Provider value={contextValue}>
      {children}
      <ConfirmationDialog
        isOpen={dialogState.isOpen}
        title={dialogState.options.title}
        message={dialogState.options.message}
        confirmText={dialogState.options.confirmText}
        cancelText={dialogState.options.cancelText}
        type={dialogState.options.type}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        loading={dialogState.loading}
      />
    </DialogContext.Provider>
  );
}

export function useDialog(): DialogContextType {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
}

// Convenience hooks for common dialog types
export function useConfirmDialog() {
  const { showConfirmation } = useDialog();
  
  return {
    confirmDelete: (itemName?: string) => 
      showConfirmation({
        title: 'Confirm Delete',
        message: itemName 
          ? `Are you sure you want to delete "${itemName}"? This action cannot be undone.`
          : 'Are you sure you want to delete this item? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
      }),
    
    confirmLogout: () =>
      showConfirmation({
        title: 'Confirm Logout',
        message: 'Are you sure you want to logout?',
        confirmText: 'Logout',
        cancelText: 'Cancel',
        type: 'warning',
      }),
    
    confirmAction: (title: string, message: string, confirmText = 'Confirm') =>
      showConfirmation({
        title,
        message,
        confirmText,
        cancelText: 'Cancel',
        type: 'default',
      }),
  };
}

export function useAlertDialog() {
  const { showAlert } = useDialog();
  
  return {
    showSuccess: (title: string, message: string) => showAlert(title, message, 'success'),
    showError: (title: string, message: string) => showAlert(title, message, 'danger'),
    showWarning: (title: string, message: string) => showAlert(title, message, 'warning'),
    showInfo: (title: string, message: string) => showAlert(title, message, 'info'),
  };
}
