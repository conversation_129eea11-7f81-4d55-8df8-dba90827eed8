// AstroConnect Prisma Schema
// Horoscope and Daily Guide Application

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum definitions
enum ZodiacSign {
  aries
  taurus
  gemini
  cancer
  leo
  virgo
  libra
  scorpio
  sagittarius
  capricorn
  aquarius
  pisces
}

enum LanguageCode {
  en
  si
}

enum HoroscopeType {
  daily
  weekly
  monthly
}

enum UserRole {
  super_admin
  admin
  user
}

// Admin model
model Admin {
  id                  String      @id @default(cuid())
  email               String      @unique
  password            String      // Hashed password
  name                String
  role                UserRole    @default(admin)
  isActive            Boolean     @default(true) @map("is_active")
  lastLogin           DateTime?   @map("last_login")
  createdBy           String?     @map("created_by") // ID of admin who created this admin
  createdAt           DateTime    @default(now()) @map("created_at")
  updatedAt           DateTime    @updatedAt @map("updated_at")

  // Self-referential relation for admin hierarchy
  creator             Admin?      @relation("AdminHierarchy", fields: [createdBy], references: [id])
  createdAdmins       Admin[]     @relation("AdminHierarchy")

  @@map("admins")
}

// User model
model User {
  id                  String      @id @default(cuid())
  email               String?     @unique
  name                String
  phoneNumber         String?     @map("phone_number")
  address             String?
  zodiacSign          ZodiacSign  @map("zodiac_sign")
  birthDate           DateTime    @map("birth_date") @db.Date
  birthTime           String?     @map("birth_time") // Format: "HH:MM" (optional)
  birthPlace          String?     @map("birth_place") // Birth city/location for horoscope calculations
  birthLatitude       Float?      @map("birth_latitude") // Latitude for precise calculations
  birthLongitude      Float?      @map("birth_longitude") // Longitude for precise calculations
  qrToken             String      @unique @default(cuid()) @map("qr_token")
  languagePreference  LanguageCode @default(en) @map("language_preference")
  createdAt           DateTime    @default(now()) @map("created_at")
  updatedAt           DateTime    @updatedAt @map("updated_at")

  // Relations
  qrCodeMappings      QrCodeMapping[]
  personalHoroscopes  PersonalHoroscope[]
  birthChart          BirthChart?

  @@map("users")
}

// QR Code Mappings model
model QrCodeMapping {
  id          String    @id @default(cuid())
  qrToken     String    @unique @map("qr_token")
  userId      String    @map("user_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  lastScanned DateTime? @map("last_scanned")
  scanCount   Int       @default(0) @map("scan_count")

  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("qr_code_mappings")
}

// Horoscopes model
model Horoscope {
  id          String        @id @default(cuid())
  zodiacSign  ZodiacSign    @map("zodiac_sign")
  type        HoroscopeType
  content     String
  date        DateTime      @db.Date
  language    LanguageCode  @default(en)
  createdAt   DateTime      @default(now()) @map("created_at")

  @@unique([zodiacSign, type, date, language])
  @@map("horoscopes")
}

// Daily Zodiac Readings model (Generated from Gemini API)
model DailyZodiacReading {
  id              String       @id @default(cuid())
  zodiacSign      ZodiacSign   @map("zodiac_sign")
  date            DateTime     @db.Date
  generalReading  String       @map("general_reading")
  loveReading     String       @map("love_reading")
  careerReading   String       @map("career_reading")
  healthReading   String       @map("health_reading")
  luckyNumber     Int          @map("lucky_number")
  luckyColor      String       @map("lucky_color")
  luckyTime       String       @map("lucky_time")
  luckyGem        String       @map("lucky_gem")
  advice          String
  mood            String       // e.g., "Optimistic", "Cautious", "Energetic"
  compatibility   String       // Compatible zodiac signs for the day
  language        LanguageCode @default(en)
  createdAt       DateTime     @default(now()) @map("created_at")
  updatedAt       DateTime     @updatedAt @map("updated_at")

  @@unique([zodiacSign, date, language])
  @@map("daily_zodiac_readings")
}

// Personal Horoscopes model (Admin-managed, user-specific) - DEPRECATED
// This will be replaced by BirthChart model
model PersonalHoroscope {
  id          String       @id @default(cuid())
  userId      String       @map("user_id")
  title       String
  content     String
  isActive    Boolean      @default(true) @map("is_active")
  language    LanguageCode @default(en)
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("personal_horoscopes")
}

// Birth Chart model - Calculated Vedic Astrology Charts (Handahana)
model BirthChart {
  id                String       @id @default(cuid())
  userId            String       @unique @map("user_id")

  // Birth Details
  birthDateTime     DateTime     @map("birth_date_time")
  birthPlace        String       @map("birth_place")
  birthLatitude     Float        @map("birth_latitude")
  birthLongitude    Float        @map("birth_longitude")
  timezone          String       // Timezone at birth location

  // Calculated Chart Data (JSON)
  planetPositions   Json         @map("planet_positions") // Positions of all planets
  housePositions    Json         @map("house_positions")  // 12 houses with cusps
  aspects           Json         // Planetary aspects
  nakshatras        Json         // Lunar mansions
  dashas            Json         // Planetary periods

  // Enhanced Vedic Chart Data (JSON)
  lagnaChart        Json?        @map("lagna_chart")      // D1 Lagna Chart
  navamsaChart      Json?        @map("navamsa_chart")    // D9 Navamsa Chart
  chandraChart      Json?        @map("chandra_chart")    // Moon-based Chart
  karakTable        Json?        @map("karak_table")      // Significator relationships
  avasthaTable      Json?        @map("avastha_table")    // Planetary states
  planetaryDetails  Json?        @map("planetary_details") // Detailed planetary info
  vimshottariDasha  Json?        @map("vimshottari_dasha") // Vimshottari Dasha periods
  ashtakavarga      Json?        @map("ashtakavarga")     // Ashtakavarga calculations

  // Chart Metadata
  ascendant         String       // Rising sign
  moonSign          String       @map("moon_sign")
  sunSign           String       @map("sun_sign")

  // Interpretations (Generated)
  generalReading    String?      @map("general_reading")
  strengthsWeaknesses String?    @map("strengths_weaknesses")
  careerGuidance    String?      @map("career_guidance")
  relationshipGuidance String?   @map("relationship_guidance")
  healthGuidance    String?      @map("health_guidance")

  // Translations
  readingsEn        Json?        @map("readings_en") // English interpretations
  readingsSi        Json?        @map("readings_si") // Sinhala interpretations

  calculatedAt      DateTime     @default(now()) @map("calculated_at")
  updatedAt         DateTime     @updatedAt @map("updated_at")

  // Relations
  user              User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("birth_charts")
}

// System Settings model
model SystemSettings {
  id                  String       @id @default(cuid())
  defaultLanguage     LanguageCode @default(en) @map("default_language")
  createdAt           DateTime     @default(now()) @map("created_at")
  updatedAt           DateTime     @updatedAt @map("updated_at")

  @@map("system_settings")
}

// Translation Cache model
model TranslationCache {
  id             String       @id @default(cuid())
  originalText   String       @map("original_text")
  translatedText String       @map("translated_text")
  sourceLanguage LanguageCode @map("source_language")
  targetLanguage LanguageCode @map("target_language")
  createdAt      DateTime     @default(now()) @map("created_at")

  @@unique([originalText, sourceLanguage, targetLanguage])
  @@map("translation_cache")
}
