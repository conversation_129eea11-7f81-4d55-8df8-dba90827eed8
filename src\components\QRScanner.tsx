'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { QRScannerProps } from '@/types';
import { Camera, X, Loader2 } from 'lucide-react';

// ZXing library interface
interface ZXingResult {
  getText(): string;
}

interface ZXingCodeReader {
  decodeFromImageData(imageData: ImageData): Promise<ZXingResult>;
}

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  const [cameraStarted, setCameraStarted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const codeReaderRef = useRef<ZXingCodeReader | null>(null);

  // Load ZXing library with better error handling
  useEffect(() => {
    let mounted = true;

    const loadLibrary = async () => {
      try {
        console.log('🔄 Loading ZXing library...');

        // Dynamic import with proper error handling
        const zxingModule = await import('@zxing/library');

        if (!mounted) return;

        // Check if BrowserQRCodeReader exists
        if (!zxingModule.BrowserQRCodeReader) {
          throw new Error('BrowserQRCodeReader not found in ZXing library');
        }

        // Test instantiation
        const testReader = new zxingModule.BrowserQRCodeReader();
        if (!testReader) {
          throw new Error('Failed to instantiate BrowserQRCodeReader');
        }

        setLibraryLoaded(true);
        console.log('✅ ZXing library loaded and tested successfully');
      } catch (err) {
        console.error('❌ Failed to load ZXing library:', err);
        if (mounted) {
          setError(`Failed to load QR scanner library: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      }
    };

    loadLibrary();

    return () => {
      mounted = false;
    };
  }, []);

  // Auto-start camera when library is loaded
  useEffect(() => {
    if (libraryLoaded && !cameraStarted && !error) {
      startCameraScanning();
    }
  }, [libraryLoaded, cameraStarted, error]);

  const createCodeReader = useCallback(async (): Promise<ZXingCodeReader | null> => {
    try {
      const zxingModule = await import('@zxing/library');
      return new zxingModule.BrowserQRCodeReader();
    } catch (err) {
      console.error('Failed to create code reader:', err);
      return null;
    }
  }, []);

  const startCameraScanning = async () => {
    if (!libraryLoaded) {
      setError('QR scanner library not loaded');
      return;
    }

    if (cameraStarted) {
      return; // Already started
    }

    try {
      setError(null);
      setIsLoading(true);
      setIsScanning(true);
      setCameraStarted(true);

      console.log('🎥 Starting camera...');

      // Get user media with better constraints
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: { ideal: 'environment' }, // Prefer back camera
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 }
        }
      });

      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        // Handle video loading with promise
        const playPromise = videoRef.current.play();
        if (playPromise !== undefined) {
          await playPromise;
        }

        // Wait for video metadata to load
        await new Promise<void>((resolve) => {
          if (videoRef.current) {
            videoRef.current.onloadedmetadata = () => {
              resolve();
            };
          }
        });

        setIsLoading(false);
        startScanning();
      }

      console.log('✅ Camera started successfully');
    } catch (err) {
      console.error('❌ Camera access failed:', err);
      let errorMessage = 'Camera access failed';

      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          errorMessage = 'Camera permission denied. Please allow camera access and try again.';
        } else if (err.name === 'NotFoundError') {
          errorMessage = 'No camera found on this device.';
        } else if (err.name === 'NotSupportedError') {
          errorMessage = 'Camera not supported on this device.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setIsLoading(false);
      setIsScanning(false);
      setCameraStarted(false);
    }
  };

  const startScanning = async () => {
    if (!libraryLoaded || !videoRef.current || !canvasRef.current) return;

    // Create code reader if not exists
    if (!codeReaderRef.current) {
      codeReaderRef.current = await createCodeReader();
    }

    if (!codeReaderRef.current) {
      setError('Failed to initialize QR code reader');
      return;
    }

    const codeReader = codeReaderRef.current;

    const scanFrame = async () => {
      try {
        if (!videoRef.current || !canvasRef.current || !isScanning) return;

        const canvas = canvasRef.current;
        const video = videoRef.current;
        const context = canvas.getContext('2d');

        if (!context) return;

        // Check if video has valid dimensions
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          // Video not ready yet, try again
          if (isScanning) {
            scanIntervalRef.current = setTimeout(scanFrame, 100);
          }
          return;
        }

        // Set canvas size to match video
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw current video frame to canvas
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Try to decode QR code from canvas
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const result = await codeReader.decodeFromImageData(imageData);

        if (result && result.getText()) {
          console.log('✅ QR Code scanned:', result.getText());
          onScanSuccess(result.getText());
          stopScanning();
          return;
        }
      } catch (err) {
        // Ignore decode errors (no QR code found), keep scanning
        // Only log unexpected errors
        if (err instanceof Error && !err.message.includes('No QR code found')) {
          console.debug('Scan frame error (continuing):', err.message);
        }
      }

      // Continue scanning if still active
      if (isScanning) {
        scanIntervalRef.current = setTimeout(scanFrame, 150); // Slightly slower for better performance
      }
    };

    // Start scanning
    scanFrame();
  };



  const stopScanning = useCallback(() => {
    console.log('🛑 Stopping scanner...');

    // Clear scanning interval
    if (scanIntervalRef.current) {
      clearTimeout(scanIntervalRef.current);
      scanIntervalRef.current = null;
    }

    // Stop video stream
    if (stream) {
      stream.getTracks().forEach(track => {
        track.stop();
        console.log('📹 Stopped video track:', track.label);
      });
      setStream(null);
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    // Clear code reader reference
    codeReaderRef.current = null;

    setIsScanning(false);
    setIsLoading(false);
    setCameraStarted(false);
  }, [stream]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, [stopScanning]);



  // Show loading state while library loads
  if (!libraryLoaded) {
    return (
      <div className="flex flex-col items-center space-y-6 p-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-white mb-2">QR Scanner</h3>
          <p className="text-gray-300 text-sm">
            Initializing camera scanner...
          </p>
        </div>

        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="w-8 h-8 text-purple-400 animate-spin mb-4" />
          <p className="text-gray-300">Loading scanner library...</p>
        </div>

        {error && (
          <div className="w-full bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-300 mb-2">
              <X size={16} />
              <span className="text-sm font-medium">Error</span>
            </div>
            <p className="text-red-200 text-sm mb-3">{error}</p>
            <button
              onClick={() => {
                setError(null);
                window.location.reload(); // Reload to retry
              }}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
            >
              Retry
            </button>
          </div>
        )}
      </div>
    );
  }

  // Camera scanner interface
  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full">
        <h3 className="text-lg font-semibold text-white">
          {isLoading ? 'Starting Camera...' : 'Camera Scanner'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors p-2"
          title="Close Scanner"
        >
          <X size={24} />
        </button>
      </div>

      {error && (
        <div className="w-full bg-red-500/20 border border-red-500/50 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-300 mb-2">
            <X size={16} />
            <span className="text-sm font-medium">Camera Error</span>
          </div>
          <p className="text-red-200 text-sm mb-3">{error}</p>
          <div className="flex gap-2">
            <button
              onClick={() => {
                setError(null);
                setCameraStarted(false);
                startCameraScanning();
              }}
              className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
            >
              Retry Camera
            </button>
            <button
              onClick={() => setError(null)}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="w-8 h-8 text-purple-400 animate-spin mb-4" />
          <p className="text-gray-300">Starting camera...</p>
          <p className="text-gray-400 text-xs mt-2">Please allow camera access when prompted</p>
        </div>
      )}

      <div className="relative w-full max-w-sm">
        <video
          ref={videoRef}
          className={`w-full rounded-lg border-2 border-purple-400 ${isLoading ? 'hidden' : 'block'}`}
          autoPlay
          playsInline
          muted
        />
        <canvas
          ref={canvasRef}
          className="hidden"
        />

        {!isLoading && !error && (
          <div className="absolute inset-0 border-2 border-purple-400 rounded-lg pointer-events-none">
            <div className="absolute top-4 left-4 w-6 h-6 border-t-2 border-l-2 border-white"></div>
            <div className="absolute top-4 right-4 w-6 h-6 border-t-2 border-r-2 border-white"></div>
            <div className="absolute bottom-4 left-4 w-6 h-6 border-b-2 border-l-2 border-white"></div>
            <div className="absolute bottom-4 right-4 w-6 h-6 border-b-2 border-r-2 border-white"></div>
          </div>
        )}
      </div>

      {!isLoading && !error && (
        <div className="text-center space-y-2">
          <p className="text-gray-300 text-sm">
            📱 Position the QR code within the frame
          </p>
          <p className="text-gray-400 text-xs">
            🔍 Scanner will automatically detect QR codes
          </p>
        </div>
      )}
    </div>
  );
}
