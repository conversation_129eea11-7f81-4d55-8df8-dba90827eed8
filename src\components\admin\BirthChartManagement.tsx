'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import { Search, Plus, Edit, Trash2, Star, Calendar, User as UserIcon, RefreshCw, Eye, MapPin, Clock } from 'lucide-react';
import { useConfirmDialog, useAlertDialog } from '@/contexts/DialogContext';
import { appLogger, LogCategory, logUserAction, logAPICall, logAPIResponse, logError } from '@/utils/app-logger';

interface BirthChart {
  id: string;
  userId: string;
  birthDateTime: string;
  birthPlace: string;
  birthLatitude: number;
  birthLongitude: number;
  timezone: string;
  ascendant: string;
  moonSign: string;
  sunSign: string;
  generalReading: string;
  strengthsWeaknesses: string;
  careerGuidance: string;
  relationshipGuidance: string;
  healthGuidance: string;
  readingsEn?: any;
  readingsSi?: any;
  calculatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    zodiacSign: string;
    birthDate: string;
    birthTime?: string;
    birthPlace?: string;
  };
}

export default function BirthChartManagement() {
  const [birthCharts, setBirthCharts] = useState<BirthChart[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedChart, setSelectedChart] = useState<BirthChart | null>(null);
  const [regenerating, setRegenerating] = useState<string | null>(null);
  const { confirmAction } = useConfirmDialog();
  const { showSuccess, showError } = useAlertDialog();

  useEffect(() => {
    fetchBirthCharts();
    fetchUsers();
  }, []);

  const fetchBirthCharts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setBirthCharts(data.data);
      } else {
        showError('Failed to fetch birth charts: ' + data.error);
      }
    } catch (error) {
      console.error('Error fetching birth charts:', error);
      showError('Error fetching birth charts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setUsers(data.data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleRegenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmAction(
      'Regenerate Birth Chart',
      `Are you sure you want to regenerate the birth chart for ${userName}? This will overwrite the existing chart.`
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts/regenerate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      // Check if response is ok first
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Parse JSON with error handling
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error('Invalid response from server');
      }

      // Check if data exists and has success property
      if (!data) {
        throw new Error('Empty response from server');
      }

      if (data.success) {
        showSuccess('Birth chart regenerated successfully!');
        fetchBirthCharts(); // Refresh the list
      } else {
        showError('Failed to regenerate birth chart: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error regenerating birth chart:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error regenerating birth chart. Please try again.';
      showError(errorMessage);
    } finally {
      setRegenerating(null);
    }
  };

  const handleGenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmAction(
      'Generate Birth Chart',
      `Generate a new birth chart for ${userName}?`
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      logUserAction('Admin Generate Birth Chart', { userId, userName }, 'admin');

      const token = localStorage.getItem('admin-token');
      logAPICall('/api/admin/birth-charts/generate', 'POST', { userId }, 'admin');

      const response = await fetch('/api/admin/birth-charts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      // Check if response is ok first
      if (!response.ok) {
        appLogger.error(LogCategory.BIRTH_CHART, 'Birth chart generation HTTP error', {
          status: response.status,
          statusText: response.statusText,
          userId
        }, 'admin');
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Parse JSON with error handling
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        appLogger.error(LogCategory.BIRTH_CHART, 'Failed to parse birth chart response', {
          parseError: (parseError as Error).message,
          userId
        }, 'admin');
        throw new Error('Invalid response from server');
      }

      // Check if data exists and has success property
      if (!data) {
        appLogger.error(LogCategory.BIRTH_CHART, 'Empty birth chart response', { userId }, 'admin');
        throw new Error('Empty response from server');
      }

      if (data.success) {
        logAPIResponse('/api/admin/birth-charts/generate', true, { userId }, 'admin');
        appLogger.info(LogCategory.BIRTH_CHART, 'Birth chart generated successfully', { userId, userName }, 'admin');
        showSuccess('Birth chart generated successfully!');
        fetchBirthCharts(); // Refresh the list
      } else {
        logAPIResponse('/api/admin/birth-charts/generate', false, { error: data.error, userId }, 'admin');
        showError('Failed to generate birth chart: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      logError(error as Error, 'Admin birth chart generation', 'admin');
      const errorMessage = error instanceof Error ? error.message : 'Error generating birth chart. Please try again.';
      showError(errorMessage);
    } finally {
      setRegenerating(null);
    }
  };

  const handleViewChart = (chart: BirthChart) => {
    setSelectedChart(chart);
    setShowViewModal(true);
  };

  const filteredCharts = birthCharts.filter(chart =>
    chart.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.zodiacSign.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const usersWithoutCharts = users.filter(user => 
    !birthCharts.some(chart => chart.userId === user.id) &&
    user.birthTime && user.birthPlace
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search birth charts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      {/* Users without birth charts */}
      {usersWithoutCharts.length > 0 && (
        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
          <h3 className="text-lg font-bold text-white mb-4 flex items-center">
            <Plus className="mr-2 text-green-400" size={20} />
            Users Ready for Birth Chart Generation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {usersWithoutCharts.map((user) => (
              <div key={user.id} className="bg-white/10 rounded-lg p-4 border border-white/20">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white">{user.name}</h4>
                  <span className="text-xs text-gray-400">{user.zodiacSign}</span>
                </div>
                <p className="text-sm text-gray-300 mb-3">{user.email}</p>
                <div className="text-xs text-gray-400 mb-3">
                  <div>📅 {new Date(user.birthDate).toLocaleDateString()}</div>
                  <div>🕐 {user.birthTime}</div>
                  <div>📍 {user.birthPlace}</div>
                </div>
                <button
                  onClick={() => handleGenerateBirthChart(user.id, user.name)}
                  disabled={regenerating === user.id}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center"
                >
                  {regenerating === user.id ? (
                    <RefreshCw className="animate-spin" size={16} />
                  ) : (
                    <>
                      <Plus size={16} className="mr-1" />
                      Generate Chart
                    </>
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Birth Charts List */}
      <div className="space-y-4">
        <h3 className="text-lg font-bold text-white flex items-center">
          <Star className="mr-2 text-yellow-400" size={20} />
          Generated Birth Charts ({filteredCharts.length})
        </h3>
        
        {filteredCharts.length > 0 ? (
          filteredCharts.map((chart) => (
            <div key={chart.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <UserIcon className="text-blue-400" size={20} />
                    <h3 className="text-lg font-bold text-white">{chart.user.name}</h3>
                    <span className="px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-300">
                      {chart.user.zodiacSign}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-sm">
                      <span className="text-gray-400">Ascendant:</span>
                      <span className="text-white ml-2 font-medium">{chart.ascendant}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-400">Moon Sign:</span>
                      <span className="text-white ml-2 font-medium">{chart.moonSign}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-400">Sun Sign:</span>
                      <span className="text-white ml-2 font-medium">{chart.sunSign}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                    <div className="flex items-center">
                      <MapPin size={14} className="mr-1" />
                      {chart.birthPlace}
                    </div>
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      {new Date(chart.birthDateTime).toLocaleDateString()}
                    </div>
                    <div className="flex items-center">
                      <Clock size={14} className="mr-1" />
                      {new Date(chart.birthDateTime).toLocaleTimeString()}
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm">
                    <span className="font-medium">Email:</span> {chart.user.email}
                  </p>
                  <p className="text-gray-400 text-xs mt-2">
                    Generated: {new Date(chart.calculatedAt).toLocaleString()}
                  </p>
                </div>

                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleViewChart(chart)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center"
                  >
                    <Eye size={16} className="mr-1" />
                    View
                  </button>
                  <button
                    onClick={() => handleRegenerateBirthChart(chart.userId, chart.user.name)}
                    disabled={regenerating === chart.userId}
                    className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center"
                  >
                    {regenerating === chart.userId ? (
                      <RefreshCw className="animate-spin" size={16} />
                    ) : (
                      <>
                        <RefreshCw size={16} className="mr-1" />
                        Regenerate
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <Star className="mx-auto mb-4 text-gray-400" size={48} />
            <p className="text-gray-400">No birth charts found</p>
          </div>
        )}
      </div>

      {/* View Chart Modal */}
      {showViewModal && selectedChart && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Birth Chart Details</h2>
              <button
                onClick={() => setShowViewModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Personal Information</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-400">Name:</span> <span className="text-white ml-2">{selectedChart.user.name}</span></div>
                    <div><span className="text-gray-400">Email:</span> <span className="text-white ml-2">{selectedChart.user.email}</span></div>
                    <div><span className="text-gray-400">Birth Place:</span> <span className="text-white ml-2">{selectedChart.birthPlace}</span></div>
                    <div><span className="text-gray-400">Birth Date & Time:</span> <span className="text-white ml-2">{new Date(selectedChart.birthDateTime).toLocaleString()}</span></div>
                    <div><span className="text-gray-400">Timezone:</span> <span className="text-white ml-2">{selectedChart.timezone}</span></div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Astrological Signs</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-400">Ascendant:</span> <span className="text-white ml-2 font-medium">{selectedChart.ascendant}</span></div>
                    <div><span className="text-gray-400">Moon Sign:</span> <span className="text-white ml-2 font-medium">{selectedChart.moonSign}</span></div>
                    <div><span className="text-gray-400">Sun Sign:</span> <span className="text-white ml-2 font-medium">{selectedChart.sunSign}</span></div>
                    <div><span className="text-gray-400">Zodiac Sign:</span> <span className="text-white ml-2 font-medium">{selectedChart.user.zodiacSign}</span></div>
                  </div>
                </div>
              </div>

              {/* Readings */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-md font-semibold text-white mb-2">General Reading</h4>
                  <p className="text-gray-300 text-sm leading-relaxed bg-white/5 p-4 rounded-lg">{selectedChart.generalReading}</p>
                </div>
                
                <div>
                  <h4 className="text-md font-semibold text-white mb-2">Strengths & Weaknesses</h4>
                  <p className="text-gray-300 text-sm leading-relaxed bg-white/5 p-4 rounded-lg">{selectedChart.strengthsWeaknesses}</p>
                </div>
                
                <div>
                  <h4 className="text-md font-semibold text-white mb-2">Career Guidance</h4>
                  <p className="text-gray-300 text-sm leading-relaxed bg-white/5 p-4 rounded-lg">{selectedChart.careerGuidance}</p>
                </div>
                
                <div>
                  <h4 className="text-md font-semibold text-white mb-2">Relationship Guidance</h4>
                  <p className="text-gray-300 text-sm leading-relaxed bg-white/5 p-4 rounded-lg">{selectedChart.relationshipGuidance}</p>
                </div>
                
                <div>
                  <h4 className="text-md font-semibold text-white mb-2">Health Guidance</h4>
                  <p className="text-gray-300 text-sm leading-relaxed bg-white/5 p-4 rounded-lg">{selectedChart.healthGuidance}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
