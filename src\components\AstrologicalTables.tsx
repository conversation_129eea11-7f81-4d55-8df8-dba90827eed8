'use client';

import React from 'react';
import { 
  KarakData, 
  AvasthaData, 
  PlanetaryDetail, 
  VimshottariDashaData, 
  AshtakavargaData 
} from '@/lib/astrology';

interface AstrologicalTablesProps {
  karakTable?: KarakData;
  avasthaTable?: AvasthaData;
  planetaryDetails?: PlanetaryDetail[];
  vimshottariDasha?: VimshottariDashaData;
  ashtakavarga?: AshtakavargaData;
}

export default function AstrologicalTables({
  karakTable,
  avasthaTable,
  planetaryDetails,
  vimshottariDasha,
  ashtakavarga
}: AstrologicalTablesProps) {
  
  const renderKarakTable = () => {
    if (!karakTable) return null;
    
    return (
      <div className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Karak</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-orange-500/30">
                <th className="text-left py-2 text-orange-300">Karak</th>
                <th className="text-left py-2 text-orange-300">Sthir</th>
                <th className="text-left py-2 text-orange-300">Chara</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(karakTable).map(([karak, data]) => (
                <tr key={karak} className="border-b border-orange-500/10">
                  <td className="py-2 text-white font-medium">{karak}</td>
                  <td className="py-2 text-yellow-300">{data.sthir}</td>
                  <td className="py-2 text-yellow-300">{data.chara}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderAvasthaTable = () => {
    if (!avasthaTable) return null;
    
    return (
      <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Avastha</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-blue-500/30">
                <th className="text-left py-2 text-blue-300">Planets</th>
                <th className="text-left py-2 text-blue-300">Jagrat</th>
                <th className="text-left py-2 text-blue-300">Baladi</th>
                <th className="text-left py-2 text-blue-300">Deeptadi</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(avasthaTable).map(([planet, states]) => (
                <tr key={planet} className="border-b border-blue-500/10">
                  <td className="py-2 text-white font-medium">{planet}</td>
                  <td className="py-2 text-blue-200">{states.jagrat}</td>
                  <td className="py-2 text-blue-200">{states.baladi}</td>
                  <td className="py-2 text-blue-200">{states.deeptadi}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderPlanetaryDetails = () => {
    if (!planetaryDetails) return null;
    
    return (
      <div className="bg-gradient-to-br from-green-900/20 to-teal-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Planetary Details</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-green-500/30">
                <th className="text-left py-2 text-green-300">Planets</th>
                <th className="text-left py-2 text-green-300">C R</th>
                <th className="text-left py-2 text-green-300">Rashi</th>
                <th className="text-left py-2 text-green-300">Longitude</th>
                <th className="text-left py-2 text-green-300">Nakshatra</th>
                <th className="text-left py-2 text-green-300">Pada</th>
                <th className="text-left py-2 text-green-300">Relation</th>
              </tr>
            </thead>
            <tbody>
              {planetaryDetails.map((detail) => (
                <tr key={detail.planet} className="border-b border-green-500/10">
                  <td className="py-2 text-white font-medium">{detail.planet}</td>
                  <td className="py-2 text-center">
                    <span className={detail.combust ? 'text-red-400' : 'text-gray-400'}>
                      {detail.combust ? 'C' : '-'}
                    </span>
                    <span className="ml-1">
                      <span className={detail.retrograde ? 'text-red-400' : 'text-gray-400'}>
                        {detail.retrograde ? 'R' : '-'}
                      </span>
                    </span>
                  </td>
                  <td className="py-2 text-green-200">{detail.rashi}</td>
                  <td className="py-2 text-green-200">{detail.longitude}</td>
                  <td className="py-2 text-green-200">{detail.nakshatra}</td>
                  <td className="py-2 text-green-200">{detail.pada}</td>
                  <td className="py-2">
                    <span className={getRelationColor(detail.relation)}>
                      {detail.relation}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderVimshottariDasha = () => {
    if (!vimshottariDasha) return null;
    
    return (
      <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Vimshottari Dasha</h3>
        
        <div className="mb-4 p-4 bg-purple-800/20 rounded-lg">
          <p className="text-purple-200">
            <span className="font-semibold">Balance of Dasha:</span> {vimshottariDasha.balance}
          </p>
          <p className="text-purple-200 mt-1">
            <span className="font-semibold">Current Dasha:</span> {vimshottariDasha.currentDasha}
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-2">
          {vimshottariDasha.periods.slice(0, 9).map((period, index) => (
            <div key={index} className="flex justify-between items-center p-2 bg-purple-800/10 rounded">
              <span className="text-white font-medium">{period.planet}</span>
              <span className="text-purple-200 text-sm">
                {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderAshtakavarga = () => {
    if (!ashtakavarga) return null;
    
    return (
      <div className="bg-gradient-to-br from-indigo-900/20 to-blue-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Prastharashtakvarga</h3>
        
        {/* Sun Table */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-yellow-300 mb-2">SUN</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-xs border border-yellow-500/30">
              <thead>
                <tr className="bg-yellow-900/20">
                  <th className="border border-yellow-500/30 p-1 text-yellow-300"></th>
                  {['Ar', 'Ta', 'Ge', 'Ca', 'Le', 'Vi', 'Li', 'Sc', 'Sa', 'Ca', 'Aq', 'Pi', 'Total'].map(sign => (
                    <th key={sign} className="border border-yellow-500/30 p-1 text-yellow-300">{sign}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN', 'Ascendant'].map((planet, planetIndex) => (
                  <tr key={planet}>
                    <td className="border border-yellow-500/30 p-1 text-white font-medium">{planet}</td>
                    {ashtakavarga.sunTable[planetIndex]?.map((score, houseIndex) => (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-200">
                        {score}
                      </td>
                    )) || Array(12).fill(0).map((_, i) => (
                      <td key={i} className="border border-yellow-500/30 p-1 text-center text-yellow-200">0</td>
                    ))}
                    <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                      {ashtakavarga.sunTable[planetIndex]?.reduce((sum, score) => sum + score, 0) || 0}
                    </td>
                  </tr>
                ))}
                <tr className="bg-yellow-900/20">
                  <td className="border border-yellow-500/30 p-1 text-yellow-300 font-bold">Total</td>
                  {Array(12).fill(0).map((_, houseIndex) => {
                    const total = ashtakavarga.sunTable.reduce((sum, row) => sum + (row[houseIndex] || 0), 0);
                    return (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                        {total}
                      </td>
                    );
                  })}
                  <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                    {ashtakavarga.sarvashtakavarga.reduce((sum, score) => sum + score, 0)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Sarvashtakavarga Summary */}
        <div className="mt-4 p-4 bg-indigo-800/20 rounded-lg">
          <h4 className="text-lg font-semibold text-indigo-300 mb-2">Sarvashtakavarga Summary</h4>
          <div className="grid grid-cols-6 gap-2 text-sm">
            {ashtakavarga.sarvashtakavarga.map((score, index) => (
              <div key={index} className="text-center">
                <div className="text-indigo-300">House {index + 1}</div>
                <div className="text-white font-bold">{score}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const getRelationColor = (relation: string): string => {
    switch (relation) {
      case 'Own': return 'text-green-400';
      case 'Friendly': return 'text-blue-400';
      case 'Enemy': return 'text-red-400';
      case 'Neutral': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderKarakTable()}
        {renderAvasthaTable()}
      </div>
      
      {renderPlanetaryDetails()}
      {renderVimshottariDasha()}
      {renderAshtakavarga()}
    </div>
  );
}
