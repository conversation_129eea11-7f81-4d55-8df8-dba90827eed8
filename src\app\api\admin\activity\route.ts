import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

interface ActivityItem {
  id: string;
  action: string;
  user: string;
  time: string;
  type: 'user_registered' | 'qr_scanned' | 'horoscope_updated' | 'content_added';
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const activities: ActivityItem[] = [];

    // Get recent user registrations (last 10)
    const recentUsers = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });

    recentUsers.forEach(user => {
      activities.push({
        id: `user_${user.id}`,
        action: 'New user registered',
        user: user.email || user.name,
        time: formatTimeAgo(user.createdAt),
        type: 'user_registered'
      });
    });

    // Get recent QR code scans (last 5)
    const recentScans = await prisma.qrCodeMapping.findMany({
      where: {
        lastScanned: {
          not: null
        }
      },
      orderBy: { lastScanned: 'desc' },
      take: 5,
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    recentScans.forEach(scan => {
      if (scan.lastScanned) {
        activities.push({
          id: `scan_${scan.id}`,
          action: 'QR code scanned',
          user: scan.user.email || scan.user.name,
          time: formatTimeAgo(scan.lastScanned),
          type: 'qr_scanned'
        });
      }
    });

    // Get recent horoscope updates (last 3)
    const recentHoroscopes = await prisma.horoscope.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        id: true,
        zodiacSign: true,
        type: true,
        createdAt: true
      }
    });

    recentHoroscopes.forEach(horoscope => {
      activities.push({
        id: `horoscope_${horoscope.id}`,
        action: `${horoscope.type} horoscope updated`,
        user: 'Admin',
        time: formatTimeAgo(horoscope.createdAt),
        type: 'horoscope_updated'
      });
    });

    // Sort all activities by time (most recent first)
    activities.sort((a, b) => {
      const timeA = parseTimeAgo(a.time);
      const timeB = parseTimeAgo(b.time);
      return timeA - timeB;
    });

    // Return top 8 activities
    const topActivities = activities.slice(0, 8);

    return NextResponse.json<ApiResponse<ActivityItem[]>>({
      success: true,
      data: topActivities,
      message: 'Recent activities retrieved successfully'
    });

  } catch (error) {
    console.error('Recent activity error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  } else {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  }
}

function parseTimeAgo(timeStr: string): number {
  if (timeStr === 'Just now') return 0;
  
  const match = timeStr.match(/(\d+)\s+(minute|hour|day)s?\s+ago/);
  if (!match) return 0;
  
  const value = parseInt(match[1]);
  const unit = match[2];
  
  switch (unit) {
    case 'minute': return value;
    case 'hour': return value * 60;
    case 'day': return value * 60 * 24;
    default: return 0;
  }
}
