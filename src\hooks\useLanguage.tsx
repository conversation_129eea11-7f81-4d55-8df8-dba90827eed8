'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { LanguageContextType } from '@/types';
import { translateText } from '@/utils/translation';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<'en' | 'si'>('en');
  const [isTranslating, setIsTranslating] = useState(false);

  const translate = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text; // No translation needed for English
    }

    // Only translate if we don't have pre-translated content
    console.log('⚠️ Using translation API for:', text.substring(0, 50) + '...');

    setIsTranslating(true);
    try {
      const translatedText = await translateText(text, language, 'en');
      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      return text; // Return original text if translation fails
    } finally {
      setIsTranslating(false);
    }
  }, [language]);

  const value: LanguageContextType = {
    language,
    setLanguage,
    translate,
    isTranslating
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Hook for translating text with caching
export function useTranslation() {
  const { language, translate, isTranslating } = useLanguage();
  const [translationCache, setTranslationCache] = useState<Map<string, string>>(new Map());

  const t = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text;
    }

    const cacheKey = `${text}_${language}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    const translatedText = await translate(text);
    setTranslationCache(prev => new Map(prev).set(cacheKey, translatedText));
    return translatedText;
  }, [language, translate, translationCache]);

  return { t, isTranslating, language };
}
