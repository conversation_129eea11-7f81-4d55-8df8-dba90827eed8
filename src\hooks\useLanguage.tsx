'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { LanguageContextType } from '@/types';
import { translateText } from '@/utils/translation';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<'en' | 'si'>('en');
  const [isTranslating, setIsTranslating] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize language - this will be overridden by user preference if available
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Only set admin default if no user preference will be loaded
        // The dashboard will handle setting user preference
        const response = await fetch('/api/settings/default-language');
        const data = await response.json();

        if (data.success && data.data.defaultLanguage) {
          setLanguage(data.data.defaultLanguage);
        }
      } catch (error) {
        console.error('Failed to load default language:', error);
        // Keep default 'en' if API fails
      } finally {
        setIsInitialized(true);
      }
    };

    if (!isInitialized) {
      initializeLanguage();
    }
  }, [isInitialized]);

  // Method to set language and persist it
  const updateLanguage = useCallback((newLanguage: 'en' | 'si') => {
    console.log('🌐 Language updated to:', newLanguage);
    setLanguage(newLanguage);
  }, []);

  const translate = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text; // No translation needed for English
    }

    // Only translate if we don't have pre-translated content
    console.log('⚠️ Using translation API for:', text.substring(0, 50) + '...');

    setIsTranslating(true);
    try {
      const translatedText = await translateText(text, language, 'en');
      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      return text; // Return original text if translation fails
    } finally {
      setIsTranslating(false);
    }
  }, [language]);

  const value: LanguageContextType = {
    language,
    setLanguage: updateLanguage,
    translate,
    isTranslating,
    isInitialized
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Hook for translating text with caching
export function useTranslation() {
  const { language, translate, isTranslating } = useLanguage();
  const [translationCache, setTranslationCache] = useState<Map<string, string>>(new Map());

  const t = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text;
    }

    const cacheKey = `${text}_${language}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    const translatedText = await translate(text);
    setTranslationCache(prev => new Map(prev).set(cacheKey, translatedText));
    return translatedText;
  }, [language, translate, translationCache]);

  return { t, isTranslating, language };
}
