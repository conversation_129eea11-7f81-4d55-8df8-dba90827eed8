'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { LanguageContextType } from '@/types';
import { translateText } from '@/utils/translation';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  // Default to Sinhala for user-facing pages, English for admin
  const getInitialLanguage = () => {
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      // Keep admin panel in English
      if (path.startsWith('/admin')) {
        return 'en';
      }
      // Default to Sinhala for user-facing pages (landing, dashboard, QR scan)
      return 'si';
    }
    return 'si'; // Default to Sinhala on server-side
  };

  const [language, setLanguage] = useState<'en' | 'si'>(getInitialLanguage);
  const [isTranslating, setIsTranslating] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize language - simplified to avoid conflicts
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        const path = window.location.pathname;

        // For admin pages, always use English
        if (path.startsWith('/admin')) {
          setLanguage('en');
        } else {
          // For user-facing pages, default to Sinhala
          // User preference will be set by the dashboard page
          setLanguage('si');
        }
      } catch (error) {
        console.error('Failed to initialize language:', error);
        // Keep path-based default if API fails
        const path = window.location.pathname;
        setLanguage(path.startsWith('/admin') ? 'en' : 'si');
      } finally {
        setIsInitialized(true);
      }
    };

    if (!isInitialized) {
      initializeLanguage();
    }
  }, [isInitialized]);

  // Method to set language and persist it
  const updateLanguage = useCallback((newLanguage: 'en' | 'si') => {
    console.log('🌐 Language update requested:', { from: language, to: newLanguage });
    if (language !== newLanguage) {
      setLanguage(newLanguage);
      console.log('✅ Language state updated to:', newLanguage);
    } else {
      console.log('⚠️ Language already set to:', newLanguage);
    }
  }, [language]);

  const translate = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text; // No translation needed for English
    }

    // Only translate if we don't have pre-translated content
    console.log('⚠️ Using translation API for:', text.substring(0, 50) + '...');

    setIsTranslating(true);
    try {
      const translatedText = await translateText(text, language, 'en');
      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      return text; // Return original text if translation fails
    } finally {
      setIsTranslating(false);
    }
  }, [language]);

  const value: LanguageContextType = {
    language,
    setLanguage: updateLanguage,
    translate,
    isTranslating,
    isInitialized
  };

  // Debug when language context value changes
  useEffect(() => {
    console.log('🔄 Language context updated:', { language, isInitialized });
  }, [language, isInitialized]);

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Hook for translating text with caching
export function useTranslation() {
  const { language, translate, isTranslating } = useLanguage();
  const [translationCache, setTranslationCache] = useState<Map<string, string>>(new Map());

  const t = useCallback(async (text: string): Promise<string> => {
    if (language === 'en') {
      return text;
    }

    const cacheKey = `${text}_${language}`;
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    const translatedText = await translate(text);
    setTranslationCache(prev => new Map(prev).set(cacheKey, translatedText));
    return translatedText;
  }, [language, translate, translationCache]);

  return { t, isTranslating, language };
}
