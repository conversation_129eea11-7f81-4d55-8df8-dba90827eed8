<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple QR Code Generator for Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        #qrcode {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-urls {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .test-url {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .test-url:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 QR Code Generator for AstroConnect Testing</h1>
        
        <div class="instructions">
            <h3>📱 How to Test:</h3>
            <ol>
                <li>Generate a QR code using the form below</li>
                <li>Open <strong>http://localhost:3000</strong> on your mobile device</li>
                <li>Click "Scan Your QR Code"</li>
                <li>Point your camera at the QR code displayed below</li>
                <li>The app should scan and process the QR code</li>
            </ol>
        </div>

        <div class="form-group">
            <label for="qrText">QR Code Content:</label>
            <input type="text" id="qrText" placeholder="Enter text or URL to encode" value="http://localhost:3000/auth?token=test123">
        </div>

        <div class="form-group">
            <label for="qrSize">QR Code Size:</label>
            <select id="qrSize">
                <option value="200">Small (200x200)</option>
                <option value="300" selected>Medium (300x300)</option>
                <option value="400">Large (400x400)</option>
                <option value="500">Extra Large (500x500)</option>
            </select>
        </div>

        <button onclick="generateQR()">🎯 Generate QR Code</button>

        <div id="qrcode">
            <p style="color: #666;">QR Code will appear here</p>
        </div>

        <div class="test-urls">
            <h3>🧪 Quick Test URLs (Click to Generate):</h3>
            <div class="test-url" onclick="generateQuickQR('http://localhost:3000/auth?token=user123')">
                🔗 Test User Token: user123
            </div>
            <div class="test-url" onclick="generateQuickQR('http://localhost:3000/auth?token=astro456')">
                🔗 Test Astro Token: astro456
            </div>
            <div class="test-url" onclick="generateQuickQR('http://localhost:3000/auth?token=cosmic789')">
                🔗 Test Cosmic Token: cosmic789
            </div>
            <div class="test-url" onclick="generateQuickQR('Hello World - QR Scanner Test')">
                📝 Simple Text: "Hello World - QR Scanner Test"
            </div>
            <div class="test-url" onclick="generateQuickQR('https://www.google.com')">
                🌐 External URL: Google.com
            </div>
        </div>
    </div>

    <script>
        function generateQR() {
            const text = document.getElementById('qrText').value;
            const size = parseInt(document.getElementById('qrSize').value);
            
            if (!text.trim()) {
                alert('Please enter some text or URL to generate QR code');
                return;
            }

            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = '<p style="color: #666;">Generating QR Code...</p>';

            QRCode.toCanvas(text, {
                width: size,
                height: size,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error, canvas) {
                if (error) {
                    qrContainer.innerHTML = '<p style="color: red;">Error generating QR code: ' + error + '</p>';
                    return;
                }
                
                qrContainer.innerHTML = '';
                qrContainer.appendChild(canvas);
                
                // Add download link
                const downloadLink = document.createElement('a');
                downloadLink.href = canvas.toDataURL();
                downloadLink.download = 'qr-code.png';
                downloadLink.textContent = '💾 Download QR Code';
                downloadLink.style.display = 'block';
                downloadLink.style.marginTop = '10px';
                downloadLink.style.color = '#667eea';
                downloadLink.style.textDecoration = 'none';
                downloadLink.style.fontWeight = 'bold';
                qrContainer.appendChild(downloadLink);
            });
        }

        function generateQuickQR(text) {
            document.getElementById('qrText').value = text;
            generateQR();
        }

        // Generate initial QR code
        window.onload = function() {
            generateQR();
        };
    </script>
</body>
</html>
