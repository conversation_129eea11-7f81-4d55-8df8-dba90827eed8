import crypto from 'crypto';

// Generate cryptographically secure random token
export function generateS<PERSON>ureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// Generate secure QR token (UUID v4)
export function generateSecureQRToken(): string {
  return crypto.randomUUID();
}

// Hash sensitive data
export function hashData(data: string, salt?: string): { hash: string; salt: string } {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');
  return { hash, salt: actualSalt };
}

// Verify hashed data
export function verifyHash(data: string, hash: string, salt: string): boolean {
  const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
  return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(verifyHash, 'hex'));
}

// Encrypt sensitive data
export function encryptData(data: string, key?: string): { encrypted: string; iv: string; key: string } {
  const actualKey = key || crypto.randomBytes(32).toString('hex');
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', actualKey);
  
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    key: actualKey
  };
}

// Decrypt sensitive data
export function decryptData(encryptedData: string, key: string, iv: string): string {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Generate HMAC signature
export function generateHMAC(data: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
}

// Verify HMAC signature
export function verifyHMAC(data: string, signature: string, secret: string): boolean {
  const expectedSignature = generateHMAC(data, secret);
  return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
}

// Generate secure session token
export function generateSessionToken(): string {
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  return `${timestamp}.${randomBytes}`;
}

// Validate session token
export function validateSessionToken(token: string, maxAge: number = 24 * 60 * 60 * 1000): boolean {
  try {
    const [timestamp, randomPart] = token.split('.');
    if (!timestamp || !randomPart) return false;
    
    const tokenTime = parseInt(timestamp, 10);
    const now = Date.now();
    
    return (now - tokenTime) <= maxAge;
  } catch {
    return false;
  }
}

// Rate limiting with sliding window
export class SlidingWindowRateLimit {
  private windows: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number,
    private windowSizeMs: number
  ) {}
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowSizeMs;
    
    // Get or create window for this key
    let requests = this.windows.get(key) || [];
    
    // Remove old requests outside the window
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if we're within the limit
    if (requests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    requests.push(now);
    this.windows.set(key, requests);
    
    return true;
  }
  
  getRemainingRequests(key: string): number {
    const now = Date.now();
    const windowStart = now - this.windowSizeMs;
    const requests = this.windows.get(key) || [];
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
  
  getResetTime(key: string): number {
    const requests = this.windows.get(key) || [];
    if (requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...requests);
    return oldestRequest + this.windowSizeMs;
  }
  
  cleanup(): void {
    const now = Date.now();
    
    for (const [key, requests] of this.windows.entries()) {
      const windowStart = now - this.windowSizeMs;
      const validRequests = requests.filter(timestamp => timestamp > windowStart);
      
      if (validRequests.length === 0) {
        this.windows.delete(key);
      } else {
        this.windows.set(key, validRequests);
      }
    }
  }
}

// IP address validation and normalization
export function normalizeIP(ip: string): string {
  // Handle IPv6 mapped IPv4 addresses
  if (ip.startsWith('::ffff:')) {
    return ip.substring(7);
  }
  
  // Handle localhost variations
  if (ip === '::1' || ip === '127.0.0.1') {
    return 'localhost';
  }
  
  return ip;
}

// Check if IP is in allowed range (for admin access)
export function isIPAllowed(ip: string, allowedRanges: string[]): boolean {
  const normalizedIP = normalizeIP(ip);
  
  // For development, allow localhost
  if (process.env.NODE_ENV === 'development' && normalizedIP === 'localhost') {
    return true;
  }
  
  // Check against allowed ranges
  return allowedRanges.some(range => {
    if (range === normalizedIP) return true;
    
    // Simple CIDR check (basic implementation)
    if (range.includes('/')) {
      const [network, prefixLength] = range.split('/');
      // This is a simplified check - in production, use a proper CIDR library
      return normalizedIP.startsWith(network.split('.').slice(0, parseInt(prefixLength) / 8).join('.'));
    }
    
    return false;
  });
}

// Secure random string generation for various purposes
export function generateSecureRandomString(length: number, charset?: string): string {
  const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const actualCharset = charset || defaultCharset;
  
  let result = '';
  const bytes = crypto.randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    result += actualCharset[bytes[i] % actualCharset.length];
  }
  
  return result;
}

// Content Security Policy nonce generation
export function generateCSPNonce(): string {
  return crypto.randomBytes(16).toString('base64');
}

// Secure headers configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

// Input sanitization for different contexts
export function sanitizeForHTML(input: string): string {
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

export function sanitizeForSQL(input: string): string {
  return input.replace(/'/g, "''");
}

export function sanitizeForJS(input: string): string {
  return input
    .replace(/\\/g, '\\\\')
    .replace(/'/g, "\\'")
    .replace(/"/g, '\\"')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
}
