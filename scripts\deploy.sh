#!/bin/bash

# AstroConnect Deployment Script
# This script handles the deployment of the AstroConnect application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="astroconnect"
DOCKER_IMAGE="astroconnect:latest"
CONTAINER_NAME="astroconnect-app"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
    fi
    
    if [ ! -f ".env.local" ]; then
        error ".env.local file not found. Please create it with required environment variables."
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    mkdir -p "$BACKUP_DIR"
    BACKUP_NAME="backup-$(date +'%Y%m%d-%H%M%S').tar.gz"
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log "Creating application backup..."
        docker exec "$CONTAINER_NAME" tar -czf "/tmp/$BACKUP_NAME" /app || warning "Backup creation failed"
        docker cp "$CONTAINER_NAME:/tmp/$BACKUP_NAME" "$BACKUP_DIR/" || warning "Backup copy failed"
    fi
    
    success "Backup created: $BACKUP_DIR/$BACKUP_NAME"
}

# Build application
build_app() {
    log "Building application..."
    
    # Build the Docker image
    docker build -t "$DOCKER_IMAGE" . || error "Docker build failed"
    
    success "Application built successfully"
}

# Deploy application
deploy_app() {
    log "Deploying application..."
    
    # Stop existing container if running
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log "Stopping existing container..."
        docker stop "$CONTAINER_NAME" || warning "Failed to stop existing container"
        docker rm "$CONTAINER_NAME" || warning "Failed to remove existing container"
    fi
    
    # Start new container
    log "Starting new container..."
    docker-compose up -d || error "Failed to start application"
    
    # Wait for application to be ready
    log "Waiting for application to be ready..."
    for i in {1..30}; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            success "Application is ready"
            return 0
        fi
        sleep 2
    done
    
    error "Application failed to start properly"
}

# Run health checks
health_check() {
    log "Running health checks..."
    
    # Check if container is running
    if ! docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        error "Container is not running"
    fi
    
    # Check application health endpoint
    HEALTH_RESPONSE=$(curl -s http://localhost:3000/api/health)
    if echo "$HEALTH_RESPONSE" | grep -q '"status":"healthy"'; then
        success "Health check passed"
    else
        error "Health check failed: $HEALTH_RESPONSE"
    fi
    
    # Check database connectivity
    log "Checking database connectivity..."
    if echo "$HEALTH_RESPONSE" | grep -q '"database":"ok"'; then
        success "Database connectivity check passed"
    else
        warning "Database connectivity check failed"
    fi
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old images and containers..."
    
    # Remove old images (keep last 3)
    docker images "$APP_NAME" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
        tail -n +4 | awk '{print $1}' | xargs -r docker rmi || warning "Failed to remove old images"
    
    # Remove unused containers
    docker container prune -f || warning "Failed to prune containers"
    
    # Remove unused images
    docker image prune -f || warning "Failed to prune images"
    
    success "Cleanup completed"
}

# Show logs
show_logs() {
    log "Showing application logs..."
    docker-compose logs -f --tail=50
}

# Main deployment process
main() {
    log "Starting deployment of $APP_NAME..."
    
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            create_backup
            build_app
            deploy_app
            health_check
            cleanup
            success "Deployment completed successfully!"
            ;;
        "logs")
            show_logs
            ;;
        "health")
            health_check
            ;;
        "backup")
            create_backup
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "Usage: $0 {deploy|logs|health|backup|cleanup}"
            echo "  deploy  - Full deployment process (default)"
            echo "  logs    - Show application logs"
            echo "  health  - Run health checks"
            echo "  backup  - Create backup only"
            echo "  cleanup - Cleanup old images and containers"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
