'use client';

import { useState, useRef, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Globe, Check, Loader2 } from 'lucide-react';
import { LANGUAGE_NAMES } from '@/utils/translation';

interface LanguageSwitcherProps {
  onLanguageChange?: (language: 'en' | 'si') => void;
  className?: string;
}

export default function LanguageSwitcher({ onLanguageChange, className = '' }: LanguageSwitcherProps) {
  const { language, setLanguage, isTranslating } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {
    if (newLanguage === language) return;

    setIsOpen(false);

    if (onLanguageChange) {
      // If there's a custom handler, use it (for dashboard)
      onLanguageChange(newLanguage);
    } else {
      // Otherwise, use the context directly (for landing page)
      setLanguage(newLanguage);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-all duration-200 border border-white/20 hover:border-white/40 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
        disabled={isTranslating}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {isTranslating ? (
          <Loader2 size={16} className="animate-spin" />
        ) : (
          <Globe size={16} />
        )}
        <span className="text-sm font-medium">{LANGUAGE_NAMES[language]}</span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute top-full right-0 mt-2 bg-gray-900/95 backdrop-blur-md border border-white/30 rounded-lg shadow-2xl z-[100] min-w-[140px] overflow-hidden transition-all duration-200 ease-out opacity-100 scale-100"
          role="menu"
          aria-orientation="vertical"
        >
          {Object.entries(LANGUAGE_NAMES).map(([code, name]) => (
            <button
              key={code}
              onClick={() => handleLanguageChange(code as 'en' | 'si')}
              className="flex items-center justify-between w-full px-4 py-3 text-white hover:bg-white/20 transition-all duration-200 first:rounded-t-lg last:rounded-b-lg focus:outline-none focus:bg-white/20"
              role="menuitem"
            >
              <span className="font-medium">{name}</span>
              {language === code && <Check size={16} className="text-green-400" />}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
