'use client';

import { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Globe, Check, Loader2 } from 'lucide-react';
import { LANGUAGE_NAMES } from '@/utils/translation';

interface LanguageSwitcherProps {
  onLanguageChange?: (language: 'en' | 'si') => void;
  className?: string;
}

export default function LanguageSwitcher({ onLanguageChange, className = '' }: LanguageSwitcherProps) {
  const { language, setLanguage, isTranslating } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {
    if (newLanguage === language) return;
    
    setLanguage(newLanguage);
    setIsOpen(false);
    
    if (onLanguageChange) {
      onLanguageChange(newLanguage);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-colors"
        disabled={isTranslating}
      >
        {isTranslating ? (
          <Loader2 size={16} className="animate-spin" />
        ) : (
          <Globe size={16} />
        )}
        <span>{LANGUAGE_NAMES[language]}</span>
        <svg 
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg z-50 min-w-[120px]">
          {Object.entries(LANGUAGE_NAMES).map(([code, name]) => (
            <button
              key={code}
              onClick={() => handleLanguageChange(code as 'en' | 'si')}
              className="flex items-center justify-between w-full px-4 py-2 text-white hover:bg-white/10 transition-colors first:rounded-t-lg last:rounded-b-lg"
            >
              <span>{name}</span>
              {language === code && <Check size={16} className="text-green-400" />}
            </button>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
