import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse, ZodiacSign } from '@/types';
import { ZODIAC_SIGNS } from '@/utils/zodiac';

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    // Get user counts by zodiac sign
    const userCounts: Record<ZodiacSign, number> = {} as Record<ZodiacSign, number>;

    for (const sign of ZODIAC_SIGNS) {
      const count = await prisma.user.count({
        where: {
          zodiacSign: sign
        }
      });
      userCounts[sign] = count;
    }

    return NextResponse.json<ApiResponse<Record<ZodiacSign, number>>>({
      success: true,
      data: userCounts,
      message: 'User counts retrieved successfully'
    });

  } catch (error) {
    console.error('User counts fetch error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
