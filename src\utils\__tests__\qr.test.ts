import { 
  generateQRToken, 
  generateQRUrl, 
  extractTokenFromUrl, 
  isValidQRToken 
} from '../qr';

// Mock QRCode library
jest.mock('qrcode', () => ({
  toDataURL: jest.fn().mockResolvedValue('data:image/png;base64,mock-qr-code')
}));

describe('QR Utilities', () => {
  describe('generateQRToken', () => {
    test('should generate a valid UUID v4 token', () => {
      const token = generateQRToken();
      expect(typeof token).toBe('string');
      expect(token).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    test('should generate unique tokens', () => {
      const token1 = generateQRToken();
      const token2 = generateQRToken();
      expect(token1).not.toBe(token2);
    });
  });

  describe('generateQRUrl', () => {
    test('should generate URL with default base URL', () => {
      const token = 'test-token-123';
      const url = generateQRUrl(token);
      expect(url).toBe('http://localhost:3000/qr/test-token-123');
    });

    test('should generate URL with custom base URL', () => {
      const token = 'test-token-123';
      const baseUrl = 'https://example.com';
      const url = generateQRUrl(token, baseUrl);
      expect(url).toBe('https://example.com/qr/test-token-123');
    });

    test('should handle base URL with trailing slash', () => {
      const token = 'test-token-123';
      const baseUrl = 'https://example.com/';
      const url = generateQRUrl(token, baseUrl);
      expect(url).toBe('https://example.com//qr/test-token-123');
    });
  });

  describe('extractTokenFromUrl', () => {
    test('should extract token from valid QR URL', () => {
      const url = 'https://example.com/qr/abc123def456';
      const token = extractTokenFromUrl(url);
      expect(token).toBe('abc123def456');
    });

    test('should extract token from URL with query parameters', () => {
      const url = 'https://example.com/qr/abc123def456?param=value';
      const token = extractTokenFromUrl(url);
      expect(token).toBe('abc123def456');
    });

    test('should extract token from URL with hash', () => {
      const url = 'https://example.com/qr/abc123def456#section';
      const token = extractTokenFromUrl(url);
      expect(token).toBe('abc123def456');
    });

    test('should return null for invalid URL format', () => {
      expect(extractTokenFromUrl('invalid-url')).toBeNull();
      expect(extractTokenFromUrl('https://example.com/other/path')).toBeNull();
      expect(extractTokenFromUrl('https://example.com/qr/')).toBeNull();
    });

    test('should return null for URL without qr path', () => {
      const url = 'https://example.com/other/abc123def456';
      const token = extractTokenFromUrl(url);
      expect(token).toBeNull();
    });

    test('should handle localhost URLs', () => {
      const url = 'http://localhost:3000/qr/test-token';
      const token = extractTokenFromUrl(url);
      expect(token).toBe('test-token');
    });
  });

  describe('isValidQRToken', () => {
    test('should validate correct UUID v4 format', () => {
      const validTokens = [
        '550e8400-e29b-41d4-a716-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8'
      ];

      validTokens.forEach(token => {
        expect(isValidQRToken(token)).toBe(true);
      });
    });

    test('should reject invalid UUID formats', () => {
      const invalidTokens = [
        'not-a-uuid',
        '550e8400-e29b-41d4-a716', // too short
        '550e8400-e29b-41d4-a716-************-extra', // too long
        '550e8400-e29b-31d4-a716-************', // wrong version (should be 4)
        '550e8400-e29b-41d4-z716-************', // invalid character
        '',
        null,
        undefined
      ];

      invalidTokens.forEach(token => {
        expect(isValidQRToken(token as string)).toBe(false);
      });
    });

    test('should handle edge cases', () => {
      expect(isValidQRToken('')).toBe(false);
      expect(isValidQRToken('   ')).toBe(false);
      expect(isValidQRToken('550e8400-e29b-41d4-a716-************ ')).toBe(false); // with space
    });
  });

  describe('generateQRCodeImage', () => {
    const QRCode = require('qrcode');

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should generate QR code image data URL', async () => {
      const { generateQRCodeImage } = require('../qr');
      const token = 'test-token';
      const result = await generateQRCodeImage(token);
      
      expect(result).toBe('data:image/png;base64,mock-qr-code');
      expect(QRCode.toDataURL).toHaveBeenCalledWith(
        'http://localhost:3000/qr/test-token',
        expect.objectContaining({
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
      );
    });

    test('should use custom base URL when provided', async () => {
      const { generateQRCodeImage } = require('../qr');
      const token = 'test-token';
      const baseUrl = 'https://custom.com';
      
      await generateQRCodeImage(token, baseUrl);
      
      expect(QRCode.toDataURL).toHaveBeenCalledWith(
        'https://custom.com/qr/test-token',
        expect.any(Object)
      );
    });

    test('should throw error when QR code generation fails', async () => {
      QRCode.toDataURL.mockRejectedValueOnce(new Error('QR generation failed'));
      
      const { generateQRCodeImage } = require('../qr');
      
      await expect(generateQRCodeImage('test-token')).rejects.toThrow('Failed to generate QR code');
    });
  });
});
