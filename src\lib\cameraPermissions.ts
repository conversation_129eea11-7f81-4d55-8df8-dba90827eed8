/**
 * Comprehensive Camera Permission Management System
 * Handles camera permissions across different browsers with proper user guidance
 */

export type PermissionState = 'granted' | 'denied' | 'prompt' | 'unknown';

export interface CameraPermissionResult {
  granted: boolean;
  state: PermissionState;
  error?: string;
  needsUserAction?: boolean;
  browserInstructions?: string;
}

export interface CameraCapabilities {
  hasCamera: boolean;
  cameraCount: number;
  supportedConstraints: MediaTrackSupportedConstraints | null;
  isSecureContext: boolean;
  browserInfo: {
    name: string;
    version: string;
    isSupported: boolean;
  };
}

/**
 * Detect browser information
 */
export function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  let browserName = 'Unknown';
  let version = 'Unknown';
  let isSupported = true;

  if (userAgent.includes('Chrome')) {
    browserName = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    version = match ? match[1] : 'Unknown';
    isSupported = parseInt(version) >= 53; // Chrome 53+ supports getUserMedia
  } else if (userAgent.includes('Firefox')) {
    browserName = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    version = match ? match[1] : 'Unknown';
    isSupported = parseInt(version) >= 36; // Firefox 36+ supports getUserMedia
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    browserName = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    version = match ? match[1] : 'Unknown';
    isSupported = parseInt(version) >= 11; // Safari 11+ supports getUserMedia
  } else if (userAgent.includes('Edge')) {
    browserName = 'Edge';
    const match = userAgent.match(/Edge\/(\d+)/);
    version = match ? match[1] : 'Unknown';
    isSupported = parseInt(version) >= 12; // Edge 12+ supports getUserMedia
  }

  return { name: browserName, version, isSupported };
}

/**
 * Get browser-specific instructions for enabling camera
 */
export function getBrowserInstructions(browserName: string): string {
  switch (browserName.toLowerCase()) {
    case 'chrome':
      return `Chrome Instructions:
1. Click the camera icon in the address bar
2. Select "Always allow" for camera access
3. Or go to Settings > Privacy > Site Settings > Camera
4. Add this site to "Allow" list`;

    case 'firefox':
      return `Firefox Instructions:
1. Click the shield icon in the address bar
2. Click "Allow" when prompted for camera access
3. Or go to Settings > Privacy & Security > Permissions > Camera
4. Find this site and change to "Allow"`;

    case 'safari':
      return `Safari Instructions:
1. Go to Safari > Settings > Websites > Camera
2. Find this website and set to "Allow"
3. Or click Safari menu > Settings for This Website > Camera > Allow`;

    case 'edge':
      return `Edge Instructions:
1. Click the camera icon in the address bar
2. Select "Allow" for camera access
3. Or go to Settings > Site permissions > Camera
4. Add this site to allowed list`;

    default:
      return `General Instructions:
1. Look for a camera icon in your browser's address bar
2. Click it and select "Allow" for camera access
3. Check your browser's privacy/security settings
4. Ensure camera permissions are enabled for this site`;
  }
}

/**
 * Check if the current environment supports camera access
 */
export async function checkCameraCapabilities(): Promise<CameraCapabilities> {
  const browserInfo = getBrowserInfo();
  
  const capabilities: CameraCapabilities = {
    hasCamera: false,
    cameraCount: 0,
    supportedConstraints: null,
    isSecureContext: window.isSecureContext,
    browserInfo
  };

  try {
    // Check if MediaDevices API is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      return capabilities;
    }

    // Get supported constraints
    if (navigator.mediaDevices.getSupportedConstraints) {
      capabilities.supportedConstraints = navigator.mediaDevices.getSupportedConstraints();
    }

    // Enumerate devices to count cameras
    if (navigator.mediaDevices.enumerateDevices) {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      capabilities.cameraCount = videoDevices.length;
      capabilities.hasCamera = videoDevices.length > 0;
    }
  } catch (error) {
    console.warn('Error checking camera capabilities:', error);
  }

  return capabilities;
}

/**
 * Check current camera permission status
 */
export async function checkCameraPermission(): Promise<CameraPermissionResult> {
  const browserInfo = getBrowserInfo();
  
  // Check if we're in a secure context
  if (!window.isSecureContext) {
    return {
      granted: false,
      state: 'denied',
      error: 'Camera access requires a secure context (HTTPS or localhost)',
      needsUserAction: true,
      browserInstructions: 'Please access this site via HTTPS or localhost'
    };
  }

  // Check if MediaDevices API is available
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    return {
      granted: false,
      state: 'denied',
      error: 'Camera API not supported in this browser',
      needsUserAction: false,
      browserInstructions: 'Please use a modern browser that supports camera access'
    };
  }

  try {
    // Try to use Permissions API if available
    if ('permissions' in navigator && 'query' in navigator.permissions) {
      try {
        const permissionStatus = await navigator.permissions.query({ name: 'camera' as PermissionName });
        
        const result: CameraPermissionResult = {
          granted: permissionStatus.state === 'granted',
          state: permissionStatus.state as PermissionState,
          needsUserAction: permissionStatus.state === 'prompt' || permissionStatus.state === 'denied'
        };

        if (permissionStatus.state === 'denied') {
          result.error = 'Camera permission has been denied';
          result.browserInstructions = getBrowserInstructions(browserInfo.name);
        }

        return result;
      } catch (permError) {
        console.warn('Permissions API query failed:', permError);
        // Fall through to getUserMedia test
      }
    }

    // Fallback: Try a quick getUserMedia test
    try {
      const testStream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 1, height: 1 } 
      });
      
      // Clean up immediately
      testStream.getTracks().forEach(track => track.stop());
      
      return {
        granted: true,
        state: 'granted',
        needsUserAction: false
      };
    } catch (getUserMediaError: any) {
      let errorMessage = 'Camera access failed';
      let state: PermissionState = 'unknown';
      let needsUserAction = true;

      if (getUserMediaError.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied';
        state = 'denied';
      } else if (getUserMediaError.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device';
        state = 'denied';
        needsUserAction = false;
      } else if (getUserMediaError.name === 'NotSupportedError') {
        errorMessage = 'Camera not supported on this device';
        state = 'denied';
        needsUserAction = false;
      } else if (getUserMediaError.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application';
        state = 'denied';
      }

      return {
        granted: false,
        state,
        error: errorMessage,
        needsUserAction,
        browserInstructions: needsUserAction ? getBrowserInstructions(browserInfo.name) : undefined
      };
    }
  } catch (error) {
    return {
      granted: false,
      state: 'unknown',
      error: 'Failed to check camera permission',
      needsUserAction: true,
      browserInstructions: getBrowserInstructions(browserInfo.name)
    };
  }
}

/**
 * Request camera permission with proper user guidance
 */
export async function requestCameraPermission(constraints?: MediaStreamConstraints): Promise<CameraPermissionResult> {
  const defaultConstraints: MediaStreamConstraints = {
    video: {
      facingMode: { ideal: 'environment' },
      width: { ideal: 1280, min: 640 },
      height: { ideal: 720, min: 480 }
    }
  };

  const finalConstraints = constraints || defaultConstraints;

  try {
    const stream = await navigator.mediaDevices.getUserMedia(finalConstraints);
    
    return {
      granted: true,
      state: 'granted',
      needsUserAction: false
    };
  } catch (error: any) {
    const browserInfo = getBrowserInfo();
    let errorMessage = 'Camera access failed';
    let state: PermissionState = 'unknown';
    let needsUserAction = true;

    if (error.name === 'NotAllowedError') {
      errorMessage = 'Camera permission denied by user';
      state = 'denied';
    } else if (error.name === 'NotFoundError') {
      errorMessage = 'No camera found on this device';
      state = 'denied';
      needsUserAction = false;
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Camera not supported on this device';
      state = 'denied';
      needsUserAction = false;
    } else if (error.name === 'NotReadableError') {
      errorMessage = 'Camera is already in use by another application';
      state = 'denied';
    } else if (error.name === 'OverconstrainedError') {
      errorMessage = 'Camera constraints not supported';
      state = 'denied';
    }

    return {
      granted: false,
      state,
      error: errorMessage,
      needsUserAction,
      browserInstructions: needsUserAction ? getBrowserInstructions(browserInfo.name) : undefined
    };
  }
}

/**
 * Get a camera stream with comprehensive error handling
 */
export async function getCameraStream(constraints?: MediaStreamConstraints): Promise<{
  stream: MediaStream | null;
  error?: string;
  permissionResult: CameraPermissionResult;
}> {
  // First check capabilities
  const capabilities = await checkCameraCapabilities();
  
  if (!capabilities.isSecureContext) {
    return {
      stream: null,
      error: 'Camera access requires HTTPS or localhost',
      permissionResult: {
        granted: false,
        state: 'denied',
        error: 'Insecure context',
        needsUserAction: true
      }
    };
  }

  if (!capabilities.hasCamera) {
    return {
      stream: null,
      error: 'No camera found on this device',
      permissionResult: {
        granted: false,
        state: 'denied',
        error: 'No camera available',
        needsUserAction: false
      }
    };
  }

  // Check current permission
  const permissionResult = await checkCameraPermission();
  
  if (permissionResult.granted) {
    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints || {
        video: {
          facingMode: { ideal: 'environment' },
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 }
        }
      });
      
      return {
        stream,
        permissionResult: {
          granted: true,
          state: 'granted'
        }
      };
    } catch (error: any) {
      return {
        stream: null,
        error: error.message,
        permissionResult: {
          granted: false,
          state: 'denied',
          error: error.message,
          needsUserAction: true
        }
      };
    }
  }

  return {
    stream: null,
    error: permissionResult.error,
    permissionResult
  };
}
