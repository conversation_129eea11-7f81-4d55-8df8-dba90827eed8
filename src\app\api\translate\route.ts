import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { text, sourceLanguage, targetLanguage } = await request.json();

    if (!text || !sourceLanguage || !targetLanguage) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Missing required parameters: text, sourceLanguage, targetLanguage'
      }, { status: 400 });
    }

    if (sourceLanguage === targetLanguage) {
      return NextResponse.json<ApiResponse<{ translatedText: string }>>({
        success: true,
        data: { translatedText: text },
        message: 'No translation needed'
      });
    }

    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Gemini API key not configured'
      }, { status: 500 });
    }

    // Prepare the prompt for Gemini
    const languageNames = {
      en: 'English',
      si: 'Sinhala'
    };

    const prompt = `Translate the following text from ${languageNames[sourceLanguage as keyof typeof languageNames]} to ${languageNames[targetLanguage as keyof typeof languageNames]}. 
    
    Important guidelines:
    - Maintain the original meaning and context
    - For astrological content, preserve the spiritual and mystical tone
    - Use appropriate cultural context for the target language
    - Return only the translated text without any additional commentary
    
    Text to translate: "${text}"`;

    // Call Gemini API
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Gemini API error:', errorData);
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Translation service unavailable'
      }, { status: 503 });
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Invalid response from translation service'
      }, { status: 500 });
    }

    const translatedText = data.candidates[0].content.parts[0].text.trim();

    return NextResponse.json<ApiResponse<{ translatedText: string }>>({
      success: true,
      data: { translatedText },
      message: 'Translation completed successfully'
    });

  } catch (error) {
    console.error('Translation API error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
