import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'AstroConnect API is working!',
    timestamp: new Date().toISOString(),
    database: 'Prisma with PostgreSQL (not connected in demo)',
    features: [
      'QR Code Authentication',
      'Multi-language Support (English/Sinhala)',
      'Daily Horoscopes',
      'Lucky Guidance',
      'Admin Panel',
      'Progressive Web App'
    ]
  });
}
