import { NextRequest, NextResponse } from 'next/server';
import { generateQRCodeImage } from '@/utils/qr';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { qrToken, userName } = await request.json();

    if (!qrToken) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'QR token is required'
      }, { status: 400 });
    }

    console.log('Generating QR code image for token:', qrToken);

    // Generate QR code image
    const qrCodeImage = await generateQRCodeImage(qrToken);

    return NextResponse.json<ApiResponse<{ qrCodeImage: string }>>({
      success: true,
      data: {
        qrCodeImage
      },
      message: `QR code generated successfully${userName ? ` for ${userName}` : ''}`
    });

  } catch (error) {
    console.error('QR generation error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to generate QR code'
    }, { status: 500 });
  }
}
