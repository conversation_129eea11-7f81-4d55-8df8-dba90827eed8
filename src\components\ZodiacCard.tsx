'use client';

import { ZodiacSign } from '@/types';
import { ZODIAC_INFO, getZodiacColors } from '@/utils/zodiac';

interface ZodiacCardProps {
  zodiacSign: ZodiacSign;
  className?: string;
}

export default function ZodiacCard({ zodiacSign, className = '' }: ZodiacCardProps) {
  const info = ZODIAC_INFO[zodiacSign];
  const colors = getZodiacColors(zodiacSign);

  return (
    <div 
      className={`relative overflow-hidden rounded-lg p-6 text-white ${className}`}
      style={{
        background: `linear-gradient(135deg, ${colors[0]}, ${colors[1]})`
      }}
    >
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-4xl">{info.symbol}</div>
          <div className="text-right">
            <h3 className="text-xl font-bold">{info.name}</h3>
            <p className="text-sm opacity-90">{info.element}</p>
          </div>
        </div>
        
        <p className="text-sm opacity-90">{info.dates}</p>
      </div>
      
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
        <div className="text-8xl transform rotate-12">{info.symbol}</div>
      </div>
    </div>
  );
}
