// Birth Chart Translation Service
// Generate translations for birth chart readings and interpretations

import { translateText } from '@/utils/translation';

export interface BirthChartReadings {
  generalReading: string;
  strengthsWeaknesses: string;
  careerGuidance: string;
  relationshipGuidance: string;
  healthGuidance: string;
}

export interface BirthChartTranslations {
  en: BirthChartReadings;
  si: BirthChartReadings;
}

/**
 * Generate translations for birth chart interpretations
 */
export async function generateBirthChartTranslations(
  interpretations: BirthChartReadings
): Promise<BirthChartTranslations> {
  console.log('🌐 Starting birth chart translation process...');

  try {
    // English is the source, so we keep it as-is
    const englishReadings: BirthChartReadings = {
      generalReading: interpretations.generalReading,
      strengthsWeaknesses: interpretations.strengthsWeaknesses,
      careerGuidance: interpretations.careerGuidance,
      relationshipGuidance: interpretations.relationshipGuidance,
      healthGuidance: interpretations.healthGuidance
    };

    // Translate to Sinhala
    console.log('🔄 Translating birth chart readings to Sinhala...');
    
    const sinhalaReadings: BirthChartReadings = {
      generalReading: await translateBirthChartText(interpretations.generalReading, 'General Reading'),
      strengthsWeaknesses: await translateBirthChartText(interpretations.strengthsWeaknesses, 'Strengths & Weaknesses'),
      careerGuidance: await translateBirthChartText(interpretations.careerGuidance, 'Career Guidance'),
      relationshipGuidance: await translateBirthChartText(interpretations.relationshipGuidance, 'Relationship Guidance'),
      healthGuidance: await translateBirthChartText(interpretations.healthGuidance, 'Health Guidance')
    };

    console.log('✅ Birth chart translations completed successfully');

    return {
      en: englishReadings,
      si: sinhalaReadings
    };

  } catch (error) {
    console.error('❌ Error generating birth chart translations:', error);
    
    // Return English-only if translation fails
    return {
      en: interpretations,
      si: interpretations // Fallback to English
    };
  }
}

/**
 * Translate a specific birth chart text with context
 */
async function translateBirthChartText(text: string, context: string): Promise<string> {
  try {
    console.log(`🔄 Translating ${context}...`);
    
    // Use the existing translation utility with astrological context
    const translatedText = await translateTextWithAstrologicalContext(text, context);
    
    console.log(`✅ ${context} translated successfully`);
    return translatedText;
    
  } catch (error) {
    console.error(`❌ Error translating ${context}:`, error);
    return text; // Return original text if translation fails
  }
}

/**
 * Translate text with astrological context for better accuracy
 */
async function translateTextWithAstrologicalContext(text: string, context: string): Promise<string> {
  try {
    // Call the Gemini API directly with astrological context
    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      throw new Error('Gemini API key not configured');
    }

    const prompt = `Translate the following ${context} from English to Sinhala. This is astrological content from a Vedic birth chart (Handahana) reading.

Important guidelines:
- Maintain the spiritual and mystical tone appropriate for astrological content
- Use traditional Sinhala astrological terminology where applicable
- Preserve the meaning and cultural context
- Keep the same paragraph structure and formatting
- Use respectful and formal Sinhala language suitable for spiritual guidance
- Return only the translated text without any additional commentary

Text to translate: "${text}"`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response from translation service');
    }

    const translatedText = data.candidates[0].content.parts[0].text.trim();
    return translatedText;

  } catch (error) {
    console.error('Translation API error:', error);
    // Fallback to the regular translation service
    return await translateText(text, 'si', 'en');
  }
}

/**
 * Update existing birth chart with translations
 */
export async function updateBirthChartWithTranslations(
  birthChartId: string,
  translations: BirthChartTranslations
): Promise<void> {
  try {
    const { prisma } = await import('@/lib/prisma');
    
    await prisma.birthChart.update({
      where: { id: birthChartId },
      data: {
        readingsEn: translations.en as any,
        readingsSi: translations.si as any
      }
    });

    console.log('✅ Birth chart updated with translations');
  } catch (error) {
    console.error('❌ Error updating birth chart with translations:', error);
    throw error;
  }
}
