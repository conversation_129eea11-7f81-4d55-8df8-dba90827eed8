// Horoscope Interpretation Service
// Generate meaningful interpretations from birth chart data

import { BirthChartData, PlanetPosition } from './astrology';

export interface HoroscopeInterpretations {
  generalReading: string;
  strengthsWeaknesses: string;
  careerGuidance: string;
  relationshipGuidance: string;
  healthGuidance: string;
}

/**
 * Generate comprehensive horoscope interpretations from birth chart
 */
export function generateHoroscopeInterpretations(chartData: BirthChartData): HoroscopeInterpretations {
  console.log('📖 Generating horoscope interpretations...');

  const generalReading = generateGeneralReading(chartData);
  const strengthsWeaknesses = generateStrengthsWeaknesses(chartData);
  const careerGuidance = generateCareerGuidance(chartData);
  const relationshipGuidance = generateRelationshipGuidance(chartData);
  const healthGuidance = generateHealthGuidance(chartData);

  return {
    generalReading,
    strengthsWeaknesses,
    careerGuidance,
    relationshipGuidance,
    healthGuidance
  };
}

/**
 * Generate general personality reading
 */
function generateGeneralReading(chartData: BirthChartData): string {
  const { ascendant, moonSign, sunSign } = chartData;
  
  let reading = `Your birth chart reveals a unique cosmic blueprint. `;
  
  // Ascendant interpretation
  reading += getAscendantInterpretation(ascendant) + ' ';
  
  // Sun sign interpretation
  reading += getSunSignInterpretation(sunSign) + ' ';
  
  // Moon sign interpretation
  reading += getMoonSignInterpretation(moonSign) + ' ';
  
  // Add planetary influences
  const strongPlanets = getStrongPlanets(chartData.planets);
  if (strongPlanets.length > 0) {
    reading += `The strong influence of ${strongPlanets.join(', ')} in your chart brings additional depth to your personality. `;
  }
  
  return reading.trim();
}

/**
 * Generate strengths and weaknesses analysis
 */
function generateStrengthsWeaknesses(chartData: BirthChartData): string {
  const { planets, ascendant } = chartData;
  
  let analysis = 'Your cosmic strengths and areas for growth:\n\n';
  
  // Analyze planetary strengths
  const strengths: string[] = [];
  const weaknesses: string[] = [];
  
  planets.forEach(planet => {
    const strength = getPlanetaryStrength(planet);
    if (strength.isStrong) {
      strengths.push(`${planet.name} in ${planet.sign}: ${strength.description}`);
    } else {
      weaknesses.push(`${planet.name} in ${planet.sign}: ${strength.description}`);
    }
  });
  
  analysis += '**Strengths:**\n';
  strengths.slice(0, 3).forEach(strength => {
    analysis += `• ${strength}\n`;
  });
  
  analysis += '\n**Areas for Growth:**\n';
  weaknesses.slice(0, 3).forEach(weakness => {
    analysis += `• ${weakness}\n`;
  });
  
  return analysis;
}

/**
 * Generate career guidance
 */
function generateCareerGuidance(chartData: BirthChartData): string {
  const { planets, houses } = chartData;
  
  let guidance = 'Career and Professional Life Guidance:\n\n';
  
  // Find 10th house (career house)
  const tenthHouse = houses.find(h => h.number === 10);
  if (tenthHouse) {
    guidance += `Your 10th house in ${tenthHouse.sign} suggests ${getCareerBySign(tenthHouse.sign)}. `;
  }
  
  // Check for planets in career-related houses
  const careerPlanets = planets.filter(p => [1, 6, 10].includes(p.house));
  if (careerPlanets.length > 0) {
    guidance += `With ${careerPlanets.map(p => p.name).join(', ')} influencing your career sector, `;
    guidance += getCareerPlanetInfluence(careerPlanets);
  }
  
  // Jupiter's influence on career
  const jupiter = planets.find(p => p.name === 'Guru');
  if (jupiter) {
    guidance += ` Jupiter in ${jupiter.sign} brings wisdom and expansion to your professional endeavors.`;
  }
  
  return guidance;
}

/**
 * Generate relationship guidance
 */
function generateRelationshipGuidance(chartData: BirthChartData): string {
  const { planets, houses } = chartData;
  
  let guidance = 'Love and Relationship Insights:\n\n';
  
  // Find 7th house (partnership house)
  const seventhHouse = houses.find(h => h.number === 7);
  if (seventhHouse) {
    guidance += `Your 7th house in ${seventhHouse.sign} indicates ${getRelationshipBySign(seventhHouse.sign)}. `;
  }
  
  // Venus influence
  const venus = planets.find(p => p.name === 'Shukra');
  if (venus) {
    guidance += `Venus in ${venus.sign} shows ${getVenusInfluence(venus.sign)} in matters of love. `;
  }
  
  // Mars influence
  const mars = planets.find(p => p.name === 'Mangal');
  if (mars) {
    guidance += `Mars in ${mars.sign} brings ${getMarsInfluence(mars.sign)} to your relationships.`;
  }
  
  return guidance;
}

/**
 * Generate health guidance
 */
function generateHealthGuidance(chartData: BirthChartData): string {
  const { planets, houses, ascendant } = chartData;
  
  let guidance = 'Health and Wellness Guidance:\n\n';
  
  // Ascendant health tendencies
  guidance += `With ${ascendant} rising, ${getHealthByAscendant(ascendant)}. `;
  
  // 6th house (health house)
  const sixthHouse = houses.find(h => h.number === 6);
  if (sixthHouse) {
    guidance += `Your 6th house in ${sixthHouse.sign} suggests ${getHealthBySign(sixthHouse.sign)}. `;
  }
  
  // Saturn's influence on health
  const saturn = planets.find(p => p.name === 'Shani');
  if (saturn) {
    guidance += `Saturn in ${saturn.sign} advises ${getSaturnHealthAdvice(saturn.sign)}.`;
  }
  
  return guidance;
}

// Helper functions for interpretations

function getAscendantInterpretation(ascendant: string): string {
  const interpretations: { [key: string]: string } = {
    'Aries': 'With Aries rising, you project confidence and leadership qualities.',
    'Taurus': 'Taurus ascendant gives you a stable and reliable appearance.',
    'Gemini': 'Gemini rising makes you appear communicative and adaptable.',
    'Cancer': 'Cancer ascendant gives you a nurturing and intuitive presence.',
    'Leo': 'Leo rising makes you appear charismatic and creative.',
    'Virgo': 'Virgo ascendant gives you a practical and analytical demeanor.',
    'Libra': 'Libra rising makes you appear harmonious and diplomatic.',
    'Scorpio': 'Scorpio ascendant gives you an intense and mysterious presence.',
    'Sagittarius': 'Sagittarius rising makes you appear optimistic and philosophical.',
    'Capricorn': 'Capricorn ascendant gives you a disciplined and ambitious appearance.',
    'Aquarius': 'Aquarius rising makes you appear innovative and independent.',
    'Pisces': 'Pisces ascendant gives you a compassionate and intuitive presence.'
  };
  return interpretations[ascendant] || 'Your rising sign influences how others perceive you.';
}

function getSunSignInterpretation(sunSign: string): string {
  const interpretations: { [key: string]: string } = {
    'Aries': 'Your Sun in Aries gives you pioneering spirit and natural leadership.',
    'Taurus': 'Sun in Taurus provides stability and appreciation for beauty.',
    'Gemini': 'Your Sun in Gemini brings curiosity and communication skills.',
    'Cancer': 'Sun in Cancer gives you emotional depth and nurturing qualities.',
    'Leo': 'Your Sun in Leo provides creativity and natural magnetism.',
    'Virgo': 'Sun in Virgo brings attention to detail and service orientation.',
    'Libra': 'Your Sun in Libra gives you diplomatic skills and love for harmony.',
    'Scorpio': 'Sun in Scorpio provides intensity and transformative power.',
    'Sagittarius': 'Your Sun in Sagittarius brings optimism and love for adventure.',
    'Capricorn': 'Sun in Capricorn gives you ambition and practical wisdom.',
    'Aquarius': 'Your Sun in Aquarius provides innovation and humanitarian ideals.',
    'Pisces': 'Sun in Pisces brings compassion and intuitive abilities.'
  };
  return interpretations[sunSign] || 'Your Sun sign represents your core identity.';
}

function getMoonSignInterpretation(moonSign: string): string {
  const interpretations: { [key: string]: string } = {
    'Aries': 'Moon in Aries gives you emotional courage and quick reactions.',
    'Taurus': 'Your Moon in Taurus provides emotional stability and comfort-seeking.',
    'Gemini': 'Moon in Gemini brings emotional versatility and need for mental stimulation.',
    'Cancer': 'Your Moon in Cancer gives deep emotional sensitivity and intuition.',
    'Leo': 'Moon in Leo provides emotional warmth and need for recognition.',
    'Virgo': 'Your Moon in Virgo brings emotional practicality and analytical nature.',
    'Libra': 'Moon in Libra gives emotional need for harmony and partnership.',
    'Scorpio': 'Your Moon in Scorpio provides emotional intensity and depth.',
    'Sagittarius': 'Moon in Sagittarius brings emotional optimism and love for freedom.',
    'Capricorn': 'Your Moon in Capricorn gives emotional discipline and ambition.',
    'Aquarius': 'Moon in Aquarius provides emotional independence and humanitarian feelings.',
    'Pisces': 'Your Moon in Pisces brings emotional compassion and psychic sensitivity.'
  };
  return interpretations[moonSign] || 'Your Moon sign represents your emotional nature.';
}

function getStrongPlanets(planets: PlanetPosition[]): string[] {
  // Simplified logic to identify strong planets
  return planets
    .filter(planet => {
      // Consider planets in their own signs or exalted signs as strong
      const strongPlacements: { [key: string]: string[] } = {
        'Surya': ['Leo'],
        'Chandra': ['Cancer'],
        'Mangal': ['Aries', 'Scorpio'],
        'Budha': ['Gemini', 'Virgo'],
        'Guru': ['Sagittarius', 'Pisces'],
        'Shukra': ['Taurus', 'Libra'],
        'Shani': ['Capricorn', 'Aquarius']
      };
      
      const strongSigns = strongPlacements[planet.name] || [];
      return strongSigns.includes(planet.sign);
    })
    .map(planet => planet.name);
}

function getPlanetaryStrength(planet: PlanetPosition): { isStrong: boolean; description: string } {
  // Simplified planetary strength analysis
  const strongPlacements: { [key: string]: { [key: string]: string } } = {
    'Surya': {
      'Leo': 'Strong leadership and confidence',
      'Aries': 'Dynamic energy and initiative'
    },
    'Chandra': {
      'Cancer': 'Deep emotional intelligence',
      'Taurus': 'Emotional stability and comfort'
    },
    'Mangal': {
      'Aries': 'Courage and pioneering spirit',
      'Scorpio': 'Transformative power and determination'
    }
    // Add more as needed
  };
  
  const planetStrengths = strongPlacements[planet.name];
  if (planetStrengths && planetStrengths[planet.sign]) {
    return {
      isStrong: true,
      description: planetStrengths[planet.sign]
    };
  }
  
  return {
    isStrong: false,
    description: 'Provides learning opportunities and growth'
  };
}

function getCareerBySign(sign: string): string {
  const careers: { [key: string]: string } = {
    'Aries': 'leadership roles, entrepreneurship, or military/sports careers',
    'Taurus': 'finance, agriculture, arts, or luxury goods industries',
    'Gemini': 'communication, media, teaching, or technology fields',
    'Cancer': 'healthcare, hospitality, real estate, or caregiving professions',
    'Leo': 'entertainment, politics, management, or creative industries',
    'Virgo': 'healthcare, analysis, service industries, or detailed work',
    'Libra': 'law, diplomacy, arts, or partnership-based businesses',
    'Scorpio': 'research, investigation, psychology, or transformative work',
    'Sagittarius': 'education, travel, philosophy, or international business',
    'Capricorn': 'administration, government, construction, or traditional businesses',
    'Aquarius': 'technology, humanitarian work, innovation, or group activities',
    'Pisces': 'healing arts, spirituality, creativity, or service to others'
  };
  return careers[sign] || 'diverse career opportunities';
}

function getCareerPlanetInfluence(planets: PlanetPosition[]): string {
  const influences = planets.map(planet => {
    const planetInfluences: { [key: string]: string } = {
      'Surya': 'leadership and authority come naturally',
      'Chandra': 'intuition guides your professional decisions',
      'Mangal': 'energy and drive propel your career forward',
      'Budha': 'communication skills are your professional asset',
      'Guru': 'wisdom and teaching abilities enhance your career',
      'Shukra': 'creativity and harmony benefit your work life',
      'Shani': 'discipline and persistence lead to career success'
    };
    return planetInfluences[planet.name] || 'brings unique qualities to your career';
  });
  
  return influences.join(', ') + '.';
}

function getRelationshipBySign(sign: string): string {
  const relationships: { [key: string]: string } = {
    'Aries': 'you seek dynamic and exciting partnerships',
    'Taurus': 'you value stability and loyalty in relationships',
    'Gemini': 'communication and mental connection are important to you',
    'Cancer': 'emotional security and nurturing define your relationships',
    'Leo': 'you seek appreciation and romance in partnerships',
    'Virgo': 'practical support and reliability matter in relationships',
    'Libra': 'harmony and balance are essential in your partnerships',
    'Scorpio': 'deep emotional intimacy and transformation characterize your relationships',
    'Sagittarius': 'freedom and shared adventures are important in partnerships',
    'Capricorn': 'commitment and long-term stability define your relationships',
    'Aquarius': 'friendship and intellectual connection form the basis of your partnerships',
    'Pisces': 'compassion and spiritual connection are vital in relationships'
  };
  return relationships[sign] || 'you have unique relationship needs';
}

function getVenusInfluence(sign: string): string {
  const influences: { [key: string]: string } = {
    'Aries': 'passionate and direct approach',
    'Taurus': 'sensual and stable love nature',
    'Gemini': 'intellectual and communicative love style',
    'Cancer': 'nurturing and protective in love',
    'Leo': 'dramatic and generous romantic expression',
    'Virgo': 'practical and devoted love approach',
    'Libra': 'harmonious and romantic nature',
    'Scorpio': 'intense and transformative love style',
    'Sagittarius': 'adventurous and freedom-loving approach',
    'Capricorn': 'serious and committed love nature',
    'Aquarius': 'unique and independent romantic style',
    'Pisces': 'compassionate and dreamy love approach'
  };
  return influences[sign] || 'a unique approach to love';
}

function getMarsInfluence(sign: string): string {
  const influences: { [key: string]: string } = {
    'Aries': 'direct and passionate energy',
    'Taurus': 'steady and sensual approach',
    'Gemini': 'mental stimulation and variety',
    'Cancer': 'protective and emotional intensity',
    'Leo': 'dramatic and confident expression',
    'Virgo': 'practical and service-oriented approach',
    'Libra': 'diplomatic and harmonious energy',
    'Scorpio': 'intense and transformative power',
    'Sagittarius': 'adventurous and philosophical approach',
    'Capricorn': 'disciplined and ambitious energy',
    'Aquarius': 'innovative and independent approach',
    'Pisces': 'intuitive and compassionate energy'
  };
  return influences[sign] || 'unique energy';
}

function getHealthByAscendant(ascendant: string): string {
  const health: { [key: string]: string } = {
    'Aries': 'you should pay attention to head and stress-related issues',
    'Taurus': 'throat and neck areas need care, maintain steady routines',
    'Gemini': 'respiratory system and nervous tension require attention',
    'Cancer': 'digestive health and emotional well-being are connected',
    'Leo': 'heart health and maintaining vitality are important',
    'Virgo': 'digestive system and attention to detail in health matters',
    'Libra': 'kidney health and maintaining balance in lifestyle',
    'Scorpio': 'reproductive health and emotional detox are important',
    'Sagittarius': 'liver health and avoiding excess are key',
    'Capricorn': 'bone health and structured health routines benefit you',
    'Aquarius': 'circulatory system and avoiding extremes in health',
    'Pisces': 'immune system and avoiding negative environments are important'
  };
  return health[ascendant] || 'maintaining overall balance is important for your health';
}

function getHealthBySign(sign: string): string {
  return getHealthByAscendant(sign); // Same logic applies
}

function getSaturnHealthAdvice(sign: string): string {
  const advice: { [key: string]: string } = {
    'Aries': 'patience and avoiding impulsive health decisions',
    'Taurus': 'consistent health routines and avoiding stubbornness about health',
    'Gemini': 'focusing on one health goal at a time',
    'Cancer': 'emotional health affects physical well-being',
    'Leo': 'humility in health matters and avoiding health-related ego',
    'Virgo': 'not being overly critical about health imperfections',
    'Libra': 'making decisive health choices rather than procrastinating',
    'Scorpio': 'releasing health-related fears and obsessions',
    'Sagittarius': 'moderation in health practices and avoiding extremes',
    'Capricorn': 'building sustainable health habits gradually',
    'Aquarius': 'following proven health methods rather than experimental ones',
    'Pisces': 'grounding health practices in reality rather than fantasy'
  };
  return advice[sign] || 'discipline and patience in health matters';
}
