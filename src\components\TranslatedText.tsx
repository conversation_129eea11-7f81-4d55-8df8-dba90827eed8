'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Loader2 } from 'lucide-react';
import { appLogger, LogCategory, logTranslation } from '@/utils/app-logger';

interface TranslatedTextProps {
  text: string;
  className?: string;
  fallback?: string;
  showLoader?: boolean;
  // New prop for pre-translated content (for daily guides, etc.)
  translations?: {
    en?: string;
    si?: string;
  };
}

function TranslatedText({
  text,
  className = '',
  fallback,
  showLoader = true,
  translations
}: TranslatedTextProps) {
  const { language, translate, isTranslating } = useLanguage();

  const [translatedText, setTranslatedText] = useState<string>(text);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const handleContent = async () => {
      const textPreview = text.substring(0, 50) + (text.length > 50 ? '...' : '');

      appLogger.debug(LogCategory.TRANSLATION, 'TranslatedText processing', {
        textPreview,
        language,
        hasTranslations: !!translations,
        translationsStructure: translations ? {
          keys: Object.keys(translations),
          hasEn: !!translations.en,
          hasSi: !!translations.si,
          enValue: translations.en?.substring(0, 50) + (translations.en && translations.en.length > 50 ? '...' : ''),
          siValue: translations.si?.substring(0, 50) + (translations.si && translations.si.length > 50 ? '...' : '')
        } : null,
        hasLanguageTranslation: !!(translations && translations[language]),
        fallbackAvailable: !!fallback
      });

      // If we have pre-translated content, use it directly (immediate update)
      if (translations && translations[language]) {
        appLogger.info(LogCategory.TRANSLATION, 'Using pre-translated content', {
          textPreview,
          language,
          translatedValue: translations[language]?.substring(0, 50) + '...'
        });
        setTranslatedText(translations[language]!);
        setIsLoading(false);
        return;
      }

      // For English, just use the original text (immediate update)
      if (language === 'en') {
        appLogger.debug(LogCategory.TRANSLATION, 'Using original English text', { textPreview });
        setTranslatedText(text);
        setIsLoading(false);
        return;
      }

      // Only use translation API for content without pre-translations
      appLogger.warn(LogCategory.TRANSLATION, 'No pre-translated content, using API', {
        textPreview,
        language,
        translationsReceived: translations,
        reasonForAPI: !translations ? 'No translations object' : !translations[language] ? 'No translation for language' : 'Unknown'
      });
      setIsLoading(true);
      try {
        const translated = await translate(text);
        logTranslation(text, 'en', language, true);
        setTranslatedText(translated);
      } catch (error) {
        logTranslation(text, 'en', language, false);
        appLogger.error(LogCategory.TRANSLATION, 'Translation API failed', {
          textPreview,
          language,
          error: (error as Error).message,
          fallbackUsed: !!fallback
        });
        setTranslatedText(fallback || text);
      } finally {
        setIsLoading(false);
      }
    };

    // Run immediately
    handleContent();
  }, [text, language, translate, fallback, translations]);

  if (isLoading && showLoader) {
    return (
      <span className={`inline-flex items-center gap-2 ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="opacity-70">{fallback || text}</span>
      </span>
    );
  }

  return <span className={className}>{translatedText}</span>;
}

// Hook for batch translation with pre-translated content support
export function useTranslatedContent(
  content: Record<string, string>,
  preTranslated?: Record<string, { en?: string; si?: string }>
) {
  const { language, translate } = useLanguage();
  const [translatedContent, setTranslatedContent] = useState<Record<string, string>>(content);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    const handleContent = async () => {
      // If we have pre-translated content, use it
      if (preTranslated) {
        const result: Record<string, string> = {};
        for (const [key, translations] of Object.entries(preTranslated)) {
          if (translations[language]) {
            result[key] = translations[language]!;
          } else {
            result[key] = content[key] || '';
          }
        }
        setTranslatedContent(result);
        return;
      }

      // For English, use original content
      if (language === 'en') {
        setTranslatedContent(content);
        return;
      }

      // Use translation API for other content
      setIsTranslating(true);
      try {
        const translations: Record<string, string> = {};

        for (const [key, text] of Object.entries(content)) {
          translations[key] = await translate(text);
        }

        setTranslatedContent(translations);
      } catch (error) {
        console.error('Batch translation error:', error);
        setTranslatedContent(content);
      } finally {
        setIsTranslating(false);
      }
    };

    handleContent();
  }, [content, language, translate, preTranslated]);

  return { translatedContent, isTranslating };
}

// Default export
export default TranslatedText;
