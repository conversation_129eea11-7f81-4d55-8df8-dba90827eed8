'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Loader2 } from 'lucide-react';

interface TranslatedTextProps {
  text: string;
  className?: string;
  fallback?: string;
  showLoader?: boolean;
  // New prop for pre-translated content (for daily guides, etc.)
  translations?: {
    en?: string;
    si?: string;
  };
}

function TranslatedText({
  text,
  className = '',
  fallback,
  showLoader = true,
  translations
}: TranslatedTextProps) {
  const { language, translate, isTranslating } = useLanguage();

  const [translatedText, setTranslatedText] = useState<string>(text);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const handleContent = async () => {
      // Debug logging for daily guide content
      if (text.includes('අද දින') || text.includes('Deep insights') || text.includes('Today') || text.includes('ආදරය')) {
        console.log('🔍 TranslatedText Debug:', {
          text: text.substring(0, 50) + '...',
          language,
          translations,
          hasTranslations: !!translations,
          hasLanguageTranslation: !!(translations && translations[language])
        });
      }

      // If we have pre-translated content, use it directly (immediate update)
      if (translations && translations[language]) {
        console.log('✅ Using pre-translated content for:', text.substring(0, 30) + '...');
        setTranslatedText(translations[language]!);
        setIsLoading(false);
        return;
      }

      // For English, just use the original text (immediate update)
      if (language === 'en') {
        setTranslatedText(text);
        setIsLoading(false);
        return;
      }

      // Only use translation API for non-daily-guide content
      console.log('⚠️ No pre-translated content, using API for:', text.substring(0, 30) + '...');
      setIsLoading(true);
      try {
        const translated = await translate(text);
        setTranslatedText(translated);
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedText(fallback || text);
      } finally {
        setIsLoading(false);
      }
    };

    // Run immediately
    handleContent();
  }, [text, language, translate, fallback, translations]);

  if (isLoading && showLoader) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="opacity-70">{fallback || text}</span>
      </div>
    );
  }

  return <span className={className}>{translatedText}</span>;
}

// Hook for batch translation with pre-translated content support
export function useTranslatedContent(
  content: Record<string, string>,
  preTranslated?: Record<string, { en?: string; si?: string }>
) {
  const { language, translate } = useLanguage();
  const [translatedContent, setTranslatedContent] = useState<Record<string, string>>(content);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    const handleContent = async () => {
      // If we have pre-translated content, use it
      if (preTranslated) {
        const result: Record<string, string> = {};
        for (const [key, translations] of Object.entries(preTranslated)) {
          if (translations[language]) {
            result[key] = translations[language]!;
          } else {
            result[key] = content[key] || '';
          }
        }
        setTranslatedContent(result);
        return;
      }

      // For English, use original content
      if (language === 'en') {
        setTranslatedContent(content);
        return;
      }

      // Use translation API for other content
      setIsTranslating(true);
      try {
        const translations: Record<string, string> = {};

        for (const [key, text] of Object.entries(content)) {
          translations[key] = await translate(text);
        }

        setTranslatedContent(translations);
      } catch (error) {
        console.error('Batch translation error:', error);
        setTranslatedContent(content);
      } finally {
        setIsTranslating(false);
      }
    };

    handleContent();
  }, [content, language, translate, preTranslated]);

  return { translatedContent, isTranslating };
}

// Default export
export default TranslatedText;
