'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Loader2 } from 'lucide-react';

interface TranslatedTextProps {
  text: string;
  className?: string;
  fallback?: string;
  showLoader?: boolean;
}

export default function TranslatedText({ 
  text, 
  className = '', 
  fallback,
  showLoader = true 
}: TranslatedTextProps) {
  const { language, translate, isTranslating } = useLanguage();
  const [translatedText, setTranslatedText] = useState<string>(text);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const translateContent = async () => {
      if (language === 'en') {
        setTranslatedText(text);
        return;
      }

      setIsLoading(true);
      try {
        const translated = await translate(text);
        setTranslatedText(translated);
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedText(fallback || text);
      } finally {
        setIsLoading(false);
      }
    };

    translateContent();
  }, [text, language, translate, fallback]);

  if (isLoading && showLoader) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="opacity-70">{fallback || text}</span>
      </div>
    );
  }

  return <span className={className}>{translatedText}</span>;
}

// Hook for batch translation
export function useTranslatedContent(content: Record<string, string>) {
  const { language, translate } = useLanguage();
  const [translatedContent, setTranslatedContent] = useState<Record<string, string>>(content);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    const translateAll = async () => {
      if (language === 'en') {
        setTranslatedContent(content);
        return;
      }

      setIsTranslating(true);
      try {
        const translations: Record<string, string> = {};
        
        for (const [key, text] of Object.entries(content)) {
          translations[key] = await translate(text);
        }
        
        setTranslatedContent(translations);
      } catch (error) {
        console.error('Batch translation error:', error);
        setTranslatedContent(content);
      } finally {
        setIsTranslating(false);
      }
    };

    translateAll();
  }, [content, language, translate]);

  return { translatedContent, isTranslating };
}
