'use client';

import { useState } from 'react';
import { ZodiacSign, HoroscopeType, LanguageCode } from '@/types';
import { X } from 'lucide-react';
import { ZODIAC_SIGNS, ZODIAC_INFO } from '@/utils/zodiac';

interface AddHoroscopeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (horoscope: any) => void;
}

export default function AddHoroscopeModal({ isOpen, onClose, onAdd }: AddHoroscopeModalProps) {
  const [formData, setFormData] = useState({
    zodiacSign: '' as ZodiacSign | '',
    type: '' as HoroscopeType | '',
    content: '',
    date: new Date().toISOString().split('T')[0],
    language: 'en' as LanguageCode
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.zodiacSign || !formData.type || !formData.content) {
      alert('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/horoscopes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (data.success) {
        onAdd(data.data);
        setFormData({
          zodiacSign: '' as ZodiacSign | '',
          type: '' as HoroscopeType | '',
          content: '',
          date: new Date().toISOString().split('T')[0],
          language: 'en' as LanguageCode
        });
        onClose();
      } else {
        alert('Error adding horoscope: ' + data.error);
      }
    } catch (error) {
      console.error('Error adding horoscope:', error);
      alert('Error adding horoscope. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white">Add New Horoscope</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Zodiac Sign *</label>
            <select
              value={formData.zodiacSign}
              onChange={(e) => setFormData({ ...formData, zodiacSign: e.target.value as ZodiacSign })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              style={{ colorScheme: 'dark' }}
              required
            >
              <option value="" className="bg-gray-800 text-white">Select zodiac sign</option>
              {ZODIAC_SIGNS.map((sign) => (
                <option key={sign} value={sign} className="bg-gray-800 text-white">
                  {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Type *</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value as HoroscopeType })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              style={{ colorScheme: 'dark' }}
              required
            >
              <option value="" className="bg-gray-800 text-white">Select type</option>
              <option value="weekly" className="bg-gray-800 text-white">Weekly</option>
              <option value="monthly" className="bg-gray-800 text-white">Monthly</option>
            </select>
            <p className="text-xs text-gray-400 mt-1">
              ℹ️ Daily guides are automatically generated using AI. Only weekly and monthly horoscopes can be created manually.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Date *</label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Language</label>
            <select
              value={formData.language}
              onChange={(e) => setFormData({ ...formData, language: e.target.value as LanguageCode })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              style={{ colorScheme: 'dark' }}
            >
              <option value="en" className="bg-gray-800 text-white">English</option>
              <option value="si" className="bg-gray-800 text-white">සිංහල</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Content *</label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              rows={6}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              placeholder="Enter horoscope content..."
              required
            />
          </div>

          <div className="flex items-center space-x-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 text-white py-2 px-4 rounded-lg transition-colors"
            >
              {loading ? 'Adding...' : 'Add Horoscope'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
