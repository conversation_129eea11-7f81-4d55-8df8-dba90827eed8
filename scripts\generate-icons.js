const fs = require('fs');
const path = require('path');

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Simple SVG icon template
const createSVGIcon = (size) => `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad)"/>
  <circle cx="${size * 0.5}" cy="${size * 0.35}" r="${size * 0.15}" fill="white" opacity="0.9"/>
  <path d="M ${size * 0.25} ${size * 0.55} Q ${size * 0.5} ${size * 0.75} ${size * 0.75} ${size * 0.55}" 
        stroke="white" stroke-width="${size * 0.05}" fill="none" opacity="0.9"/>
  <text x="${size * 0.5}" y="${size * 0.9}" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="${size * 0.08}" opacity="0.8">AC</text>
</svg>`;

// Create SVG icons
const sizes = [144, 192, 512];
sizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const svgPath = path.join(iconsDir, `icon-${size}x${size}.svg`);
  fs.writeFileSync(svgPath, svgContent);
  console.log(`Created ${svgPath}`);
});

// Create a simple favicon.ico placeholder (as SVG for now)
const faviconSVG = createSVGIcon(32);
const faviconPath = path.join(__dirname, '../public/favicon.svg');
fs.writeFileSync(faviconPath, faviconSVG);
console.log(`Created ${faviconPath}`);

console.log('Icon generation complete!');
